import { isValidIP } from '@/utils/valid-ip.js';

// 从 IPCheck.ing 获取 IPv6/4 地址
const getIPFromIPChecking64 = async (originalSite) => {
    const source = "𝓌𝑜𝒷IP IPv6/4";

    // 首先尝试通过您的快速 API 获取 IP
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip)) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试原始 IPCheck.ing API
    try {
        let ip;
        originalSite ? ip = await getFromJson() : ip = await getFromTrace();
        if (isValidIP(ip)) {
            return {
                ip: ip,
                source: source
            };
        }
    } catch (error) {
        // 所有方法都失败
    }

    return {
        ip: null,
        source: source
    };
};

const getFromJson = async () => {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        const response = await fetch("https://64.ipcheck.ing", {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data = await response.json();
        const ip = data.ip;
        return ip;
    } catch (error) {
        // 如果 JSON API 失败，尝试 trace API
        return getFromTrace();
    }
};

const getFromTrace = async () => {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        const response = await fetch("https://64.ipcheck.ing/cdn-cgi/trace", {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data = await response.text();
        const lines = data.split("\n");
        const ipLine = lines.find((line) => line.startsWith("ip="));
        let ip = "";
        if (ipLine) {
            ip = ipLine.split("=")[1].trim();
        }
        return ip;
    } catch (error) {
        throw error;
    }
};

export { getIPFromIPChecking64 };