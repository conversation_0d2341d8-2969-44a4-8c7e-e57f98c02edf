// Referer检查中间件
import { RefererChecker } from '../common/referer-check.js';

/**
 * Referer检查中间件
 * 用于验证请求的来源域名是否在允许列表中
 */
const refererMiddleware = (req, res, next) => {
  try {
    // 获取Referer头
    const referer = req.headers.referer || req.headers.referrer;
    
    // 从环境变量获取允许的域名列表
    const envAllowedDomains = (process.env.ALLOWED_DOMAINS || '').split(',').filter(d => d.trim());
    
    // 执行Referer检查
    if (!RefererChecker.refererCheck(referer, envAllowedDomains)) {

      
      return res.status(403).json({
        error: 'Access denied',
        message: 'Invalid referer or unauthorized domain',
        code: 'REFERER_CHECK_FAILED'
      });
    }
    
    // Referer检查通过，继续处理请求

    next();
    
  } catch (error) {

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Referer validation failed',
      code: 'REFERER_CHECK_ERROR'
    });
  }
};

/**
 * 可选的Referer检查中间件
 * 当Referer检查失败时记录日志但不阻止请求
 */
const optionalRefererMiddleware = (req, res, next) => {
  try {
    const referer = req.headers.referer || req.headers.referrer;
    const envAllowedDomains = (process.env.ALLOWED_DOMAINS || '').split(',').filter(d => d.trim());
    
    if (!RefererChecker.refererCheck(referer, envAllowedDomains)) {

      // 添加标记但不阻止请求
      req.suspiciousReferer = true;
    }
    
    next();
    
  } catch (error) {

    next(); // 出错时也继续处理请求
  }
};

export { refererMiddleware, optionalRefererMiddleware };
