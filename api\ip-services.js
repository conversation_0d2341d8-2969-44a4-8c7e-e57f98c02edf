// 合并的IP服务API
// import { refererMiddleware } from '../middleware/referer-middleware.js';

// 简化的referer检查中间件
const refererMiddleware = (req, res, next) => {
  // 在Vercel环境中简化referer检查
  next();
};

// IP-SB API
const ipSbHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IP.SB API
    const response = await fetch(`https://api.ip.sb/geoip/${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const result = {
      ip: data.ip || targetIP,
      city: data.city || 'N/A',
      region: data.region || 'N/A',
      country: data.country_code || 'N/A',
      country_name: data.country || 'N/A',
      country_code: data.country_code || 'N/A',
      latitude: data.latitude !== undefined ? data.latitude : 'N/A',
      longitude: data.longitude !== undefined ? data.longitude : 'N/A',
      asn: data.asn ? `AS${data.asn}` : 'N/A',
      isp: data.isp || data.organization || 'N/A',
      postal: 'N/A',
      timezone: data.timezone || 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('IP.SB API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IP.SB' });
  }
};

// IP2Location.io API
const ip2locationHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IP2Location.io API (需要API key)
    const apiKey = process.env.IP2LOCATION_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ error: 'IP2Location API key not configured' });
    }

    const response = await fetch(`https://api.ip2location.io/?ip=${encodeURIComponent(targetIP)}&key=${apiKey}&format=json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const result = {
      ip: data.ip || targetIP,
      city: data.city_name || 'N/A',
      region: data.region_name || 'N/A',
      country: data.country_code || 'N/A',
      country_name: data.country_name || 'N/A',
      country_code: data.country_code || 'N/A',
      latitude: data.latitude !== undefined ? data.latitude : 'N/A',
      longitude: data.longitude !== undefined ? data.longitude : 'N/A',
      asn: data.asn ? `AS${data.asn}` : 'N/A',
      isp: data.as || 'N/A',
      postal: data.zip_code || 'N/A',
      timezone: data.time_zone || 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('IP2Location API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IP2Location.io' });
  }
};

// IPAPI.com API
const ipapiComHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IP-API.com API
    const response = await fetch(`http://ip-api.com/json/${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const result = {
      ip: data.query || targetIP,
      city: data.city || 'N/A',
      region: data.regionName || 'N/A',
      country: data.countryCode || 'N/A',
      country_name: data.country || 'N/A',
      country_code: data.countryCode || 'N/A',
      latitude: data.lat !== undefined ? data.lat : 'N/A',
      longitude: data.lon !== undefined ? data.lon : 'N/A',
      asn: data.as ? data.as.split(' ')[0] : 'N/A',
      isp: data.isp || data.org || 'N/A',
      postal: data.zip || 'N/A',
      timezone: data.timezone || 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('IPAPI.com API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.com' });
  }
};

// IPAPI.is API
const ipapiIsHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IPAPI.is API (需要API key)
    const apiKey = process.env.IPAPIIS_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ error: 'IPAPI.is API key not configured' });
    }

    const response = await fetch(`https://api.ipapi.is?q=${encodeURIComponent(targetIP)}&key=${apiKey}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const result = {
      ip: data.ip || targetIP,
      city: data.location?.city || 'N/A',
      region: data.location?.state || 'N/A',
      country: data.location?.country_code || 'N/A',
      country_name: data.location?.country || 'N/A',
      country_code: data.location?.country_code || 'N/A',
      latitude: data.location?.latitude !== undefined ? data.location.latitude : 'N/A',
      longitude: data.location?.longitude !== undefined ? data.location.longitude : 'N/A',
      asn: data.asn?.asn ? `AS${data.asn.asn}` : 'N/A',
      isp: data.company?.name || data.asn?.org || 'N/A',
      postal: data.location?.zip || 'N/A',
      timezone: data.location?.timezone || 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('IPAPI.is API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.is' });
  }
};

// IPCheck.ing API
const ipcheckHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // IPCheck.ing需要API key和endpoint
    const apiKey = process.env.IPCHECKING_API_KEY;
    const apiEndpoint = process.env.IPCHECKING_API_ENDPOINT;

    if (!apiKey || !apiEndpoint) {
      return res.status(500).json({ error: 'IPCheck.ing API credentials not configured' });
    }

    const response = await fetch(`${apiEndpoint}/ipinfo?key=${apiKey}&ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('IPCheck.ing API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPCheck.ing' });
  }
};

// IPInfo.io API
const ipinfoHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IPInfo.io API
    const apiToken = process.env.IPINFO_API_TOKEN;
    const apiUrl = apiToken
      ? `https://ipinfo.io/${encodeURIComponent(targetIP)}?token=${apiToken}`
      : `https://ipinfo.io/${encodeURIComponent(targetIP)}/json`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const [latitudeStr, longitudeStr] = (data.loc || 'N/A,N/A').split(',');
    const latitude = parseFloat(latitudeStr);
    const longitude = parseFloat(longitudeStr);

    const orgParts = (data.org || '').split(' ');
    const asn = orgParts.length > 1 && orgParts[0].toUpperCase().startsWith('AS') ? orgParts.shift() : 'N/A';
    const org = orgParts.join(' ') || 'N/A';

    const result = {
      ip: data.ip || targetIP,
      city: data.city || 'N/A',
      region: data.region || 'N/A',
      country: data.country || 'N/A',
      country_name: data.country || 'N/A',
      country_code: data.country || 'N/A',
      latitude: !isNaN(latitude) ? latitude : 'N/A',
      longitude: !isNaN(longitude) ? longitude : 'N/A',
      asn: asn,
      isp: org,
      postal: data.postal || 'N/A',
      timezone: data.timezone || 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('IPInfo.io API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPInfo.io' });
  }
};

// MaxMind API
const maxmindHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // MaxMind需要账户ID和许可证密钥
    const accountId = process.env.MAXMIND_ACCOUNT_ID;
    const licenseKey = process.env.MAXMIND_LICENSE_KEY;

    if (!accountId || !licenseKey) {
      return res.status(500).json({ error: 'MaxMind credentials not configured' });
    }

    const response = await fetch(`https://geoip.maxmind.com/geoip/v2.1/city/${encodeURIComponent(targetIP)}`, {
      headers: {
        'Authorization': 'Basic ' + Buffer.from(`${accountId}:${licenseKey}`).toString('base64')
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('MaxMind API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from MaxMind' });
  }
};

// 美团IP API
const meituanHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用美团API (需要AppKey)
    const appKey = process.env.MEITUAN_APP_KEY || 'yourAppKey';
    const response = await fetch(`https://apimobile.meituan.com/locate/v2/ip/loc?client_source=${appKey}&rgeo=true&ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 转换为统一格式
    const result = {
      ip: targetIP,
      city: data.data?.rgeo?.city || 'N/A',
      region: data.data?.rgeo?.province || 'N/A',
      country: data.data?.rgeo?.country || 'N/A',
      country_name: data.data?.rgeo?.country || 'N/A',
      country_code: data.data?.rgeo?.country === '中国' ? 'CN' : 'N/A',
      latitude: data.data?.lat || 'N/A',
      longitude: data.data?.lng || 'N/A',
      asn: 'N/A',
      isp: data.data?.rgeo?.district || 'N/A',
      postal: data.data?.rgeo?.adcode || 'N/A',
      timezone: 'N/A',
      raw_data: data
    };

    res.json(result);
  } catch (error) {
    console.error('Meituan IP API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from Meituan IP' });
  }
};

// IPAPI.co API
const ipapiCoHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 直接调用IPAPI.co API
    const response = await fetch(`https://ipapi.co/${encodeURIComponent(targetIP)}/json/`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('IPAPI.co API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.co' });
  }
};

// 主处理函数
export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // 应用referer中间件
  refererMiddleware(req, res, async () => {
    const { service } = req.query;
    
    switch (service) {
      case 'ip-sb':
        return ipSbHandler(req, res);
      case 'ip2location':
        return ip2locationHandler(req, res);
      case 'ipapi-com':
        return ipapiComHandler(req, res);
      case 'ipapi-is':
        return ipapiIsHandler(req, res);
      case 'ipcheck':
        return ipcheckHandler(req, res);
      case 'ipinfo':
        return ipinfoHandler(req, res);
      case 'maxmind':
        return maxmindHandler(req, res);
      case 'meituan':
        return meituanHandler(req, res);
      case 'ipapi-co':
        return ipapiCoHandler(req, res);
      default:
        res.status(400).json({ error: 'Invalid service parameter' });
    }
  });
}
