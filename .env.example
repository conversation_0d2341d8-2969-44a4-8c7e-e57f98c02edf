# 服务器端口配置
BACKEND_PORT=11966
FRONTEND_PORT=18966

# 安全配置
SECURITY_RATE_LIMIT=0
SECURITY_DELAY_AFTER=0
SECURITY_BLACKLIST_LOG_FILE_PATH=logs/blacklist-ip.log

# API Keys - 根据需要配置
# IPInfo.io API Token (可选，不配置会使用免费版本)
IPINFO_API_TOKEN=your_ipinfo_token_here

# IP2Location API Key (需要注册获取)
IP2LOCATION_API_KEY=your_ip2location_key_here

# IPAPI.is API Key (需要注册获取)
IPAPIIS_API_KEY=your_ipapiis_key_here

# MaxMind GeoIP2 配置 (需要注册获取)
MAXMIND_ACCOUNT_ID=your_maxmind_account_id
MAXMIND_LICENSE_KEY=your_maxmind_license_key

# IPCheck.ing API 配置 (需要注册获取)
IPCHECKING_API_KEY=your_ipchecking_key_here
IPCHECKING_API_ENDPOINT=your_ipchecking_endpoint_here

# 美团API配置 (可选)
MEITUAN_APP_KEY=yourAppKey

# Google Maps API Key (用于地图功能)
GOOGLE_MAP_API_KEY=your_google_maps_key_here

# MAC地址查询API Key (可选)
MAC_LOOKUP_API_KEY=your_mac_lookup_key_here

# Cloudflare API Key (用于Radar功能)
CLOUDFLARE_API=your_cloudflare_api_key_here

# 允许的Referer域名 (用于安全检查)
ALLOWED_REFERER_DOMAINS=localhost,127.0.0.1,yourdomain.com
