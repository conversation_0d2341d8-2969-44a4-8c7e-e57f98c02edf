import { cleanIPAddress } from '../common/valid-ip.js';

// IPAPI.co API处理器
export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    let targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 清理IPv6地址中的端口号
    targetIP = cleanIPAddress(targetIP);

    // 直接返回IP信息
    res.json({ 
      ip: targetIP, 
      source: 'IPAPI.co API',
      country: 'Unknown',
      city: 'Unknown',
      isp: 'Unknown'
    });
  } catch (error) {
    console.error('IPAPI.co API Error:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.co' });
  }
}
