import { getIPFromCloudflare_CN } from "./cloudflare-cn";
import { isValidIP } from '@/utils/valid-ip.js';

// 从 Upai 获取 IP 地址
const getIPFromUpai = async () => {
    const source = "Upai";

    // 首先尝试通过您的 API 获取 IP
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip)) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试原始 Upai API
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const unixTime = Date.now();
        const url = `https://pubstatic.b0.upaiyun.com/?_upnode&t=${unixTime}`;
        const response = await fetch(url, {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const data = await response.json();
            const ip = data.remote_addr;
            if (isValidIP(ip)) {
                return { ip: ip, source: source };
            }
        }
    } catch (error) {
        // 继续到下一个备用方案
    }

    // 最后的故障转移：尝试从 Cloudflare 中国获取 IP 地址
    const { ip, source: fallbackSource } = await getIPFromCloudflare_CN();
    return {
        ip: ip,
        source: fallbackSource
    };
};

export { getIPFromUpai };