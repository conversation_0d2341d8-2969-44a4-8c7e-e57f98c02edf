// API适配器 - 将旧的API调用映射到新的合并API
const API_BASE = '/api';

// API映射配置
const API_MAPPING = {
  // IP服务相关
  'ip-sb': { endpoint: 'ip-services', params: { service: 'ip-sb' } },
  'ip2location-io': { endpoint: 'ip-services', params: { service: 'ip2location' } },
  'ipapi-com': { endpoint: 'ip-services', params: { service: 'ipapi-com' } },
  'ipapi-is': { endpoint: 'ip-services', params: { service: 'ipapi-is' } },
  'ipcheck-ing': { endpoint: 'ip-services', params: { service: 'ipcheck' } },
  'ipinfo-io': { endpoint: 'ip-services', params: { service: 'ipinfo' } },
  'maxmind': { endpoint: 'ip-services', params: { service: 'maxmind' } },
  'meituan-ip': { endpoint: 'ip-services', params: { service: 'meituan' } },
  
  // 工具相关
  'dns-resolver': { endpoint: 'tools', params: { tool: 'dns' } },
  'get-whois': { endpoint: 'tools', params: { tool: 'whois' } },
  'mac-checker': { endpoint: 'tools', params: { tool: 'mac' } },
  'ping-test': { endpoint: 'tools', params: { tool: 'ping' } },
  'mtr-test': { endpoint: 'tools', params: { tool: 'mtr' } },
  
  // 用户相关
  'get-user-info': { endpoint: 'user', params: { action: 'info' } },
  'update-user-achievement': { endpoint: 'user', params: { action: 'achievement' } },
  'configs': { endpoint: 'user', params: { action: 'configs' } },
  'get-real-ip': { endpoint: 'user', params: { action: 'real-ip' } },
  
  // 外部服务
  'cf-radar': { endpoint: 'external-services', params: { service: 'cf-radar' } },
  'google-map': { endpoint: 'external-services', params: { service: 'google-map' } },
  'invisibility-test': { endpoint: 'external-services', params: { service: 'invisibility' } }
};

/**
 * 通用API调用函数
 * @param {string} originalEndpoint - 原始API端点名称
 * @param {Object} params - 请求参数
 * @param {Object} options - 请求选项
 * @returns {Promise} - API响应
 */
export async function callAPI(originalEndpoint, params = {}, options = {}) {
  const mapping = API_MAPPING[originalEndpoint];
  
  if (!mapping) {
    throw new Error(`Unknown API endpoint: ${originalEndpoint}`);
  }
  
  // 构建URL
  const url = new URL(`${API_BASE}/${mapping.endpoint}`, window.location.origin);
  
  // 添加映射参数
  Object.keys(mapping.params).forEach(key => {
    url.searchParams.append(key, mapping.params[key]);
  });
  
  // 添加用户参数
  Object.keys(params).forEach(key => {
    if (params[key] !== undefined && params[key] !== null) {
      url.searchParams.append(key, params[key]);
    }
  });
  
  // 默认请求选项
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options
  };
  
  // 如果是POST请求且有body数据
  if (defaultOptions.method === 'POST' && params) {
    defaultOptions.body = JSON.stringify(params);
    // POST请求时不添加到URL参数中
    url.search = '';
    Object.keys(mapping.params).forEach(key => {
      url.searchParams.append(key, mapping.params[key]);
    });
  }
  
  try {
    const response = await fetch(url.toString(), defaultOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {

    throw error;
  }
}

/**
 * 便捷的API调用方法
 */
export const api = {
  // IP服务
  getIPFromSB: () => callAPI('ip-sb'),
  getIPFromIP2Location: () => callAPI('ip2location-io'),
  getIPFromIPAPICom: () => callAPI('ipapi-com'),
  getIPFromIPAPIIs: () => callAPI('ipapi-is'),
  getIPFromIPCheck: () => callAPI('ipcheck-ing'),
  getIPFromIPInfo: () => callAPI('ipinfo-io'),
  getIPFromMaxMind: () => callAPI('maxmind'),
  getIPFromMeituan: () => callAPI('meituan-ip'),
  
  // 工具
  resolveDNS: (domain, type, resolver) => callAPI('dns-resolver', { domain, type, resolver }),
  getWhois: (domain) => callAPI('get-whois', { domain }),
  checkMAC: (mac) => callAPI('mac-checker', { mac }),
  pingTest: (host, count) => callAPI('ping-test', { host, count }),
  mtrTest: (host, count) => callAPI('mtr-test', { host, count }),
  
  // 用户
  getUserInfo: () => callAPI('get-user-info'),
  updateAchievement: (achievement, userId) => callAPI('update-user-achievement', { achievement, userId }, { method: 'POST' }),
  getConfigs: () => callAPI('configs'),
  getRealIP: () => callAPI('get-real-ip'),
  
  // 外部服务
  getCFRadar: (endpoint, params) => callAPI('cf-radar', { endpoint, ...params }),
  getGoogleMap: (service, params) => callAPI('google-map', { service, ...params }),
  testInvisibility: (ip, service) => callAPI('invisibility-test', { ip, service })
};

export default api;
