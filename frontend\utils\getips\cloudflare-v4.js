import { isValidIP } from '@/utils/valid-ip.js';

// 从 Cloudflare 获取 IPv4 地址
const getIPFromCloudflare_V4 = async () => {
    const source = "Cloudflare IPv4";

    // 首先尝试通过本地 API 获取 IP
    try {
        const response = await fetch('/api/ip-services?service=ipwho');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip) && !data.ip.includes(':')) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试多个公共端点
    const endpoints = [
        "https://*******/cdn-cgi/trace",
        "https://*******/cdn-cgi/trace",
        "https://cloudflare.com/cdn-cgi/trace",
        "https://www.cloudflare.com/cdn-cgi/trace",
        // 备用端点
        "https://ipv4.icanhazip.com",
        "https://api.ipify.org",
        "https://ipinfo.io/ip"
    ];

    for (const endpoint of endpoints) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(endpoint, {
                signal: controller.signal,
                headers: {
                    'Accept': 'text/plain'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) continue;

            const data = await response.text();
            let ip = '';

            // 处理不同格式的响应
            if (endpoint.includes('cdn-cgi/trace')) {
                const lines = data.split("\n");
                const ipLine = lines.find((line) => line.startsWith("ip="));
                if (ipLine) {
                    ip = ipLine.split("=")[1].trim();
                }
            } else {
                // 对于其他端点，直接使用响应文本作为IP
                ip = data.trim();
            }

            if (isValidIP(ip) && !ip.includes(':')) { // 确保是IPv4
                return {
                    ip: ip,
                    source: source
                };
            }
        } catch (error) {
            // 继续尝试下一个端点
            continue;
        }
    }

    // 所有端点都失败，返回空结果
    return {
        ip: null,
        source: source
    };
};

export { getIPFromCloudflare_V4 };