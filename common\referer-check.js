import dotenv from 'dotenv';
dotenv.config();

class RefererChecker {
  // Referer 检查 (保持用户提供的原始逻辑，包含硬编码域名)
  static refererCheck(referer, allowedDomains = []) { // <-- 这个 allowedDomains 才是实际接收的参数
    if (!referer) return false;
    try {
      const url = new URL(referer);
      const hostname = url.hostname;
      const defaultAllowed = ['ip.wobshare.us.kg', 'wobip.pages.dev', 'localhost', 'localhost:18969', '127.0.0.1', 'wobshare.us.kg'];
      // 更新为项目实际使用的域名
      const allAllowed = defaultAllowed.concat(allowedDomains);

      // 添加调试日志，确认 allAllowed 的最终内容

      // 检查是否匹配
      const isAllowed = allAllowed.includes(hostname);


      return isAllowed;
    } catch (error) {

      return false;
    }
  }
}

// 兼容旧的导出方式
function refererCheck(referer, allowedDomains = []) {
  return RefererChecker.refererCheck(referer, allowedDomains);
}

export { RefererChecker, refererCheck };
