<template>
  <!-- MAC Address Checker -->
  <div class="mac-checker-section my-4">
    <div class="text-secondary">
      <p>{{ t('macchecker.Note') }}</p>
    </div>
    <div class="row">
      <div class="col-12 mb-3">
        <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
          <div class="card-body">
            <div class="col-12 col-md-auto">
              <label for="macInput" class="col-form-label">{{ t('macchecker.Note2') }}</label>
            </div>

            <div class="input-group mb-2 mt-2">
              <input
                type="text"
                class="form-control"
                :class="{ 'dark-mode': isDarkMode }"
                :disabled="macCheckStatus === 'running'"
                :placeholder="t('macchecker.Placeholder')"
                v-model="macInput"
                @keyup.enter="handleQuery"
                name="macInput"
                id="macInput"
                data-1p-ignore
              />
              <button
                class="btn btn-primary"
                @click="handleQuery"
                :disabled="macCheckStatus === 'running' || !macInput"
              >
                <span v-if="macCheckStatus === 'idle'">
                  {{ t('macchecker.Run') }}
                </span>
                <span v-if="macCheckStatus === 'running'" class="spinner-grow spinner-grow-sm" aria-hidden="true"></span>
              </button>
            </div>

            <div class="jn-placeholder">
              <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
            </div>

            <!-- Results Table -->
            <div v-if="macCheckResult && macCheckResult.success">
              <div class="alert alert-success" v-if="!errorMsg">{{ t('macchecker.successMsg') }}</div>
              <div class="table-responsive text-nowrap">
                <table class="table table-hover" :class="{ 'table-dark': isDarkMode }">
                  <thead>
                    <tr>
                      <th scope="col">{{ t('macchecker.property') }}</th>
                      <th scope="col">{{ t('macchecker.value') }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ t('macchecker.macPrefix') }}</td>
                      <td><code>{{ macCheckResult.macPrefix }}</code></td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.company') }}</td>
                      <td><strong>{{ macCheckResult.company }}</strong></td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.country') }}</td>
                      <td>{{ macCheckResult.country }}</td>
                    </tr>
                    <tr v-if="macCheckResult.type && macCheckResult.type !== 'N/A'">
                      <td>{{ t('macchecker.type') }}</td>
                      <td>{{ macCheckResult.type }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.address') }}</td>
                      <td>{{ macCheckResult.address }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.blockStart') }}</td>
                      <td>{{ macCheckResult.blockStart }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.blockEnd') }}</td>
                      <td>{{ macCheckResult.blockEnd }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.blockSize') }}</td>
                      <td>{{ macCheckResult.blockSize }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.blockType') }}</td>
                      <td>{{ macCheckResult.blockType }}</td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.isMulticast') }}</td>
                      <td>
                        <span :class="macCheckResult.isMulticast ? 'text-success' : 'text-muted'">
                          {{ macCheckResult.isMulticast ? t('macchecker.yes') : t('macchecker.no') }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.isUnicast') }}</td>
                      <td>
                        <span :class="macCheckResult.isUnicast ? 'text-success' : 'text-muted'">
                          {{ macCheckResult.isUnicast ? t('macchecker.yes') : t('macchecker.no') }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.isGlobal') }}</td>
                      <td>
                        <span :class="macCheckResult.isGlobal ? 'text-success' : 'text-muted'">
                          {{ macCheckResult.isGlobal ? t('macchecker.yes') : t('macchecker.no') }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td>{{ t('macchecker.isLocal') }}</td>
                      <td>
                        <span :class="macCheckResult.isLocal ? 'text-success' : 'text-muted'">
                          {{ macCheckResult.isLocal ? t('macchecker.yes') : t('macchecker.no') }}
                        </span>
                      </td>
                    </tr>
                    <tr v-if="macCheckResult.source">
                      <td>{{ t('macchecker.source') }}</td>
                      <td><small class="text-muted">{{ macCheckResult.source }}</small></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMainStore } from '@/store.js';

const { t } = useI18n();
const store = useMainStore();

// 暗色模式支持
const isDarkMode = computed(() => store.isDarkMode);

// 响应式数据
const macInput = ref('');
const macCheckResult = ref(null);
const macCheckStatus = ref('idle'); // 'idle', 'running'
const errorMsg = ref('');
const initialCheckDone = ref(false);

// 页面加载时的初始化
onMounted(() => {

});

// 输入验证函数
const validateInput = (input) => {
  if (!input || input.trim() === '') {
    return { isValid: false, error: t('macchecker.inputRequired') };
  }

  // 移除所有分隔符并转换为大写
  const cleanInput = input.replace(/[:-]/g, '').toUpperCase();

  // 检查是否只包含十六进制字符
  if (!/^[0-9A-F]+$/.test(cleanInput)) {
    return { isValid: false, error: t('macchecker.invalidMacCharacters') };
  }

  // 检查长度（MAC地址应该是12个十六进制字符）
  if (cleanInput.length !== 12) {
    return { isValid: false, error: t('macchecker.invalidMacLength') };
  }

  return { isValid: true, cleanInput };
};

// 处理查询请求
const handleQuery = async () => {
  const input = macInput.value.trim();
  
  // 验证输入
  const validation = validateInput(input);
  if (!validation.isValid) {
    errorMsg.value = validation.error;
    macCheckResult.value = null;
    return;
  }

  // 执行查询
  await getMacInfo(validation.cleanInput);
};

// 获取 MAC 信息
const getMacInfo = async (query) => {
  macCheckStatus.value = 'running';
  try {
    // 将纯十六进制字符串转换为标准MAC地址格式
    const formatMacAddress = (hexString) => {
      // 确保是12位十六进制字符
      const paddedHex = hexString.padEnd(12, '0').substring(0, 12);
      // 添加冒号分隔符
      return paddedHex.match(/.{2}/g).join(':').toUpperCase();
    };

    const formattedMac = formatMacAddress(query);


    // 修正 API URL 构造：使用模板字面量
    const response = await fetch(`/api/tools?tool=mac&mac=${encodeURIComponent(formattedMac)}`);

    if (!response.ok) {
      let fetchErrorMsg = `Network response was not ok (${response.status} ${response.statusText})`;
      try {
        const errorText = await response.text();
        fetchErrorMsg += ` - ${errorText.substring(0, 200)}`; // 截取部分错误信息
      } catch (e) { /* ignore */ }
      throw new Error(fetchErrorMsg);
    }

    const data = await response.json();


    // 检查是否有错误信息
    if (data.error) {
      macCheckResult.value = { success: false, found: false };
      errorMsg.value = data.error;
      return;
    }

    // 检查是否找到MAC地址信息 (后端返回found字段)
    if (data.found) {
      // MAC 地址找到，映射数据
      macCheckResult.value = {
        success: true,
        found: true,
        macPrefix: data.oui || 'N/A',
        company: data.company || 'N/A',
        country: data.country || 'N/A',
        type: data.type || 'N/A',
        address: 'N/A', // 后端没有提供地址信息
        blockStart: 'N/A', // 后端没有提供区块信息
        blockEnd: 'N/A',
        blockSize: 'N/A',
        blockType: 'N/A',
        isMulticast: false, // 后端没有提供，默认false
        isUnicast: true, // 大多数MAC地址是单播
        isLocal: false, // 后端没有提供，默认false
        isGlobal: true, // 大多数MAC地址是全球唯一
        isRand: false, // 后端没有提供，默认false
        isPrivate: false, // 后端没有提供，默认false
        source: data.source || 'Unknown',
        lastUpdate: 'N/A'
      };
      errorMsg.value = ''; // 清空错误信息
    } else {
      // MAC 地址未找到
      macCheckResult.value = {
        success: true, // API 请求本身是成功的
        found: false, // 但 MAC 未找到
        macPrefix: data.oui || 'N/A',
        company: data.company || 'Unknown Vendor',
        country: data.country || 'Unknown',
        type: data.type || 'Unknown',
        address: 'N/A',
        blockStart: 'N/A',
        blockEnd: 'N/A',
        blockSize: 'N/A',
        blockType: 'N/A',
        isMulticast: false,
        isUnicast: false,
        isLocal: false,
        isGlobal: false,
        isRand: false,
        isPrivate: false,
        source: data.source || 'Unknown',
        lastUpdate: 'N/A'
      };
      errorMsg.value = data.message || t('macchecker.macNotFound');
    }

  } catch (error) {

    macCheckStatus.value = 'idle';
    errorMsg.value = t('macchecker.fetchError') + (error.message ? `: ${error.message.substring(0, 200)}` : '');
    macCheckResult.value = { success: false, found: false }; // 确保在错误时也设置状态
  } finally {
    macCheckStatus.value = 'idle';
    initialCheckDone.value = true; // 标记查询已完成
  }
};

</script>
