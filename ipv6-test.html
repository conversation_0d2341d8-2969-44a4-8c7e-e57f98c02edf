<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPv6连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .loading {
            background-color: #cce7ff;
            color: #004085;
        }
        .result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .summary {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🌐 IPv6连接测试工具</h1>
    
    <div class="test-container">
        <h2>网络环境检测</h2>
        <button onclick="runAllTests()">开始全面测试</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-item" id="test-basic">
            <h3>1. 基础IPv6连接测试</h3>
            <button onclick="testBasicIPv6()">测试</button>
            <div class="status loading" id="status-basic">未测试</div>
            <div class="result" id="result-basic"></div>
        </div>

        <div class="test-item" id="test-dns">
            <h3>2. IPv6 DNS解析测试</h3>
            <button onclick="testIPv6DNS()">测试</button>
            <div class="status loading" id="status-dns">未测试</div>
            <div class="result" id="result-dns"></div>
        </div>

        <div class="test-item" id="test-apis">
            <h3>3. IPv6 API服务测试</h3>
            <button onclick="testIPv6APIs()">测试</button>
            <div class="status loading" id="status-apis">未测试</div>
            <div class="result" id="result-apis"></div>
        </div>

        <div class="test-item" id="test-local">
            <h3>4. 本地API IPv6支持测试</h3>
            <button onclick="testLocalIPv6()">测试</button>
            <div class="status loading" id="status-local">未测试</div>
            <div class="result" id="result-local"></div>
        </div>
    </div>

    <div class="summary" id="summary" style="display: none;">
        <h2>📊 测试总结</h2>
        <div id="summary-content"></div>
    </div>

    <script>
        const ipv6TestEndpoints = [
            { name: 'Google IPv6 DNS', url: 'https://dns.google/resolve?name=ipv6.google.com&type=AAAA' },
            { name: 'Cloudflare IPv6', url: 'https://[2606:4700:4700::1111]/cdn-cgi/trace' },
            { name: 'IPv6 Test Site', url: 'https://test-ipv6.com/json/' }
        ];

        const ipv6APIs = [
            { name: 'IPify IPv6', url: 'https://api6.ipify.org?format=json' },
            { name: 'IPv6 CanHazIP', url: 'https://ipv6.icanhazip.com' },
            { name: 'IPv6 Ident.me', url: 'https://v6.ident.me' }
        ];

        async function testBasicIPv6() {
            updateStatus('basic', 'loading', '测试中...');
            let results = [];
            
            try {
                // 测试IPv6连接性
                const response = await fetch('https://test-ipv6.com/json/', { 
                    signal: AbortSignal.timeout(10000) 
                });
                
                if (response.ok) {
                    const data = await response.json();
                    results.push(`IPv6连接测试: ${data.proto || '未知'}`);
                    results.push(`IPv6地址: ${data.address || '无'}`);
                    results.push(`IPv6可达性: ${data.v6 || '不可达'}`);
                    
                    if (data.v6 === 'reachable') {
                        updateStatus('basic', 'success', '✅ IPv6连接正常');
                    } else {
                        updateStatus('basic', 'warning', '⚠️ IPv6连接受限');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                results.push(`错误: ${error.message}`);
                updateStatus('basic', 'error', '❌ IPv6连接失败');
            }
            
            document.getElementById('result-basic').textContent = results.join('\n');
        }

        async function testIPv6DNS() {
            updateStatus('dns', 'loading', '测试中...');
            let results = [];
            let successCount = 0;
            
            for (const endpoint of ipv6TestEndpoints) {
                try {
                    const response = await fetch(endpoint.url, { 
                        signal: AbortSignal.timeout(5000) 
                    });
                    
                    if (response.ok) {
                        results.push(`✅ ${endpoint.name}: 连接成功`);
                        successCount++;
                    } else {
                        results.push(`❌ ${endpoint.name}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint.name}: ${error.message}`);
                }
            }
            
            if (successCount > 0) {
                updateStatus('dns', 'success', `✅ ${successCount}/${ipv6TestEndpoints.length} DNS测试通过`);
            } else {
                updateStatus('dns', 'error', '❌ 所有DNS测试失败');
            }
            
            document.getElementById('result-dns').textContent = results.join('\n');
        }

        async function testIPv6APIs() {
            updateStatus('apis', 'loading', '测试中...');
            let results = [];
            let successCount = 0;
            
            for (const api of ipv6APIs) {
                try {
                    const response = await fetch(api.url, { 
                        signal: AbortSignal.timeout(5000) 
                    });
                    
                    if (response.ok) {
                        const data = await response.text();
                        if (data.includes(':')) {
                            results.push(`✅ ${api.name}: ${data.trim()}`);
                            successCount++;
                        } else {
                            results.push(`⚠️ ${api.name}: 返回IPv4 - ${data.trim()}`);
                        }
                    } else {
                        results.push(`❌ ${api.name}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${api.name}: ${error.message}`);
                }
            }
            
            if (successCount > 0) {
                updateStatus('apis', 'success', `✅ ${successCount}/${ipv6APIs.length} API测试通过`);
            } else {
                updateStatus('apis', 'error', '❌ 所有IPv6 API测试失败');
            }
            
            document.getElementById('result-apis').textContent = results.join('\n');
        }

        async function testLocalIPv6() {
            updateStatus('local', 'loading', '测试中...');
            let results = [];
            
            try {
                // 测试本地API是否支持IPv6查询
                const testIPs = [
                    '2001:4860:4860::8888',  // Google IPv6 DNS
                    '2606:4700:4700::1111'   // Cloudflare IPv6 DNS
                ];
                
                for (const testIP of testIPs) {
                    try {
                        const response = await fetch(`/api/ip-services?service=ipwho&ip=${testIP}`);
                        if (response.ok) {
                            const data = await response.json();
                            results.push(`✅ 本地API IPv6查询 (${testIP}): 成功`);
                            results.push(`   城市: ${data.city}`);
                            results.push(`   国家: ${data.country_name}`);
                            results.push(`   ISP: ${data.isp}`);
                        } else {
                            results.push(`❌ 本地API IPv6查询 (${testIP}): HTTP ${response.status}`);
                        }
                    } catch (error) {
                        results.push(`❌ 本地API IPv6查询 (${testIP}): ${error.message}`);
                    }
                }
                
                updateStatus('local', 'success', '✅ 本地API支持IPv6查询');
            } catch (error) {
                results.push(`错误: ${error.message}`);
                updateStatus('local', 'error', '❌ 本地API测试失败');
            }
            
            document.getElementById('result-local').textContent = results.join('\n');
        }

        async function runAllTests() {
            await testBasicIPv6();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testIPv6DNS();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testIPv6APIs();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testLocalIPv6();
            
            showSummary();
        }

        function updateStatus(testId, type, message) {
            const statusEl = document.getElementById(`status-${testId}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function clearResults() {
            const tests = ['basic', 'dns', 'apis', 'local'];
            tests.forEach(test => {
                updateStatus(test, 'loading', '未测试');
                document.getElementById(`result-${test}`).textContent = '';
            });
            document.getElementById('summary').style.display = 'none';
        }

        function showSummary() {
            const summary = document.getElementById('summary');
            const content = document.getElementById('summary-content');
            
            const statuses = ['basic', 'dns', 'apis', 'local'].map(test => {
                const statusEl = document.getElementById(`status-${test}`);
                return {
                    test: test,
                    status: statusEl.className.includes('success') ? 'success' : 
                           statusEl.className.includes('warning') ? 'warning' : 'error',
                    message: statusEl.textContent
                };
            });
            
            let summaryText = '';
            const successCount = statuses.filter(s => s.status === 'success').length;
            const warningCount = statuses.filter(s => s.status === 'warning').length;
            const errorCount = statuses.filter(s => s.status === 'error').length;
            
            summaryText += `📈 测试结果统计:\n`;
            summaryText += `✅ 成功: ${successCount}\n`;
            summaryText += `⚠️ 警告: ${warningCount}\n`;
            summaryText += `❌ 失败: ${errorCount}\n\n`;
            
            if (successCount === 0 && errorCount > 2) {
                summaryText += `🔍 诊断结果: 您的网络环境不支持IPv6连接\n`;
                summaryText += `💡 建议: \n`;
                summaryText += `1. 联系网络服务提供商启用IPv6\n`;
                summaryText += `2. 检查路由器IPv6设置\n`;
                summaryText += `3. 暂时使用IPv4功能\n`;
            } else if (successCount > 0) {
                summaryText += `🎉 您的网络部分支持IPv6！\n`;
                summaryText += `💡 建议: 检查具体失败的服务\n`;
            }
            
            content.textContent = summaryText;
            summary.style.display = 'block';
        }
    </script>
</body>
</html>
