import { isValidIP } from '@/utils/valid-ip.js';

// 从 IPify 获取 IPv4 地址
const getIPFromIpify_V4 = async () => {
    const source = "IPify IPv4";

    // 首先尝试通过您的 API 获取 IP
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip) && !data.ip.includes(':')) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试多个 IPv4 端点
    const endpoints = [
        "https://api4.ipify.org?format=json",
        "https://api.ipify.org?format=json",
        "https://ipv4.icanhazip.com",
        "https://ipinfo.io/ip"
    ];

    for (const endpoint of endpoints) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(endpoint, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                let ip = '';
                if (endpoint.includes('ipify.org')) {
                    const data = await response.json();
                    ip = data.ip;
                } else {
                    ip = (await response.text()).trim();
                }

                if (isValidIP(ip) && !ip.includes(':')) { // 确保是IPv4
                    return {
                        ip: ip,
                        source: source
                    };
                }
            }
        } catch (error) {
            // 继续尝试下一个端点
            continue;
        }
    }

    // 所有方法都失败
    return {
        ip: null,
        source: source
    };
};

export { getIPFromIpify_V4 };