import { getIPFromUpai } from "./upai";
import { isValidIP } from '@/utils/valid-ip.js';

// 从 IPIP.net 获取 IP 地址
const getIPFromIPIP = async () => {
    const source = "𝓌𝑜𝒷.IPAPI";

    // 首先尝试通过您的 API 获取 IP
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip)) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试原始 IPIP.net API
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch("https://myip.ipip.net/json", {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const data = await response.json();
            const ip = data.data?.ip;
            if (isValidIP(ip)) {
                return {
                    ip: ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续到下一个备用方案
    }

    // 最后的故障转移：尝试从 Upai 获取 IP 地址
    const { ip, source: fallbackSource } = await getIPFromUpai();
    return {
        ip: ip,
        source: fallbackSource
    };
};

export { getIPFromIPIP };