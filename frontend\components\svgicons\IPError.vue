<template>
    <svg :style="{ fill: isDarkMode ? '#000' : '#212529', width: isMobile ? '100' : '150', height: isMobile ? '100' : '150' }"
        viewBox="0 0 653 594" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M257.84,107.122666 C291.62,107.192666 308.32,48.7626663 355.98,109.882666 C359.14,113.932666 362.16,118.482666 365.07,123.502666 C381.08,151.152666 393.66,193.272666 406.3,246.772666 C459.11,255.012666 494.55,278.712666 494.61,303.392666 C494.67,326.022666 467.65,343.142666 426.44,354.862666 C430.32,361.532666 429.51,363.632666 424.88,375.652666 C446.81,385.072666 481.92,419.782666 511,460.632666 L411.86,529.502666 L449.12,594.352666 L64.71,594.352666 L100.03,529.502666 L0,461.892666 C25.71,427.362666 57.87,387.942666 88.65,374.962666 C84.98,364.652666 84.34,363.212666 87.05,354.832666 C45.84,343.322666 18.42,326.652666 17.38,304.912666 C16.22,280.912666 50.98,257.412666 107.28,246.772666 C116.87,193.602666 129.83,151.432666 147.2,123.612666 C151.99,115.922666 157.13,109.332666 162.62,103.902666 C213.69,53.4326663 218.67,107.042666 257.84,107.122666 Z M121.21,357.052666 L120.89,358.932666 L120.3,362.492666 L116.82,383.362666 C86.43,376.642666 103.46,455.132666 131.08,448.232666 C135.3,460.412666 136.442197,471.44711 142.34,480.422666 C171.368144,524.599077 205.648487,576.791988 256.040415,576.791988 C306.432342,576.791988 341.594767,520.778379 368.99,480.422666 C375.513955,470.812277 376.14,465.142666 380.53,451.392666 C405.97,455.452666 426.88,378.082666 395.87,383.762666 L392.68,362.712666 L392.13,359.062666 L391.9,357.522666 C384.43,358.682666 128.26,357.992666 121.21,357.052666 Z M306.780508,528.766602 C306.780508,540.019255 306.204953,548.515009 296.816215,554.061498 C286.632212,560.077798 271.985838,562.842479 257.386147,562.76502 C242.467564,562.685868 227.597726,559.639061 217.587657,554.061498 C207.043139,548.186143 206.785954,541.666158 206.77972,529.607102 L206.779566,528.766602 C218.286957,534.470688 235.027101,537.322731 257,537.322731 C278.972899,537.322731 295.566401,534.470688 306.780508,528.766602 Z M240.26,495.332666 C241.64,495.332666 242.92,495.772666 243.96,496.502666 C247.73,498.602666 251.42,499.832666 254.97,499.922666 C256.256726,499.95538 257.551379,499.837477 258.853959,499.554552 C261.134997,499.059103 263.440344,498.057596 265.77,496.472666 C266.837339,495.754341 268.093464,495.368108 269.38,495.362666 L282.16,495.332666 C285.73,495.332666 288.62,498.232666 288.62,501.802666 C288.62,505.372666 285.73,508.272666 282.16,508.272666 L271.21,508.272666 C265.75,511.542666 260.23,512.942666 254.67,512.802666 C249.23,512.662666 243.89,511.032666 238.66,508.272666 L229.74,508.272666 C226.17,508.272666 223.27,505.372666 223.27,501.802666 C223.27,498.232666 226.17,495.332666 229.74,495.332666 L240.26,495.332666 Z M189.15409,466.371704 C222.824258,455.335802 288.837937,454.207694 323.84137,466.371704 C339.802073,471.918194 340.780508,480.413948 340.780508,491.666601 C329.487749,486.08157 301.27906,483.289054 256.154442,483.289054 L254.805703,483.289892 C210.46233,483.345184 182.453932,486.13742 170.780508,491.666601 L170.780697,490.917689 C170.789268,478.800023 171.183249,472.261917 189.15409,466.371704 Z M355,365 C356.656854,365 358,366.343146 358,368 L358,431 C358,432.656854 356.656854,434 355,434 L350,434 C348.343146,434 347,432.656854 347,431 L347,368 C347,366.343146 348.343146,365 350,365 L355,365 Z M385,365 C386.656854,365 388,366.343146 388,368 L388,416 C388,417.656854 386.656854,419 385,419 L380,419 C378.343146,419 377,417.656854 377,416 L377,368 C377,366.343146 378.343146,365 380,365 L385,365 Z M370,365 C371.656854,365 373,366.343146 373,368 L373,407 C373,408.656854 371.656854,410 370,410 L365,410 C363.343146,410 362,408.656854 362,407 L362,368 C362,366.343146 363.343146,365 365,365 L370,365 Z M190,376 C196.627417,376 202,381.372583 202,388 C202,394.627417 196.627417,400 190,400 C183.372583,400 178,394.627417 178,388 C178,381.372583 183.372583,376 190,376 Z M321,376 C327.627417,376 333,381.372583 333,388 C333,394.627417 327.627417,400 321,400 C314.372583,400 309,394.627417 309,388 C309,381.372583 314.372583,376 321,376 Z M256.254843,149.111758 L229.254843,169.616983 L229.254843,286.775426 L256.254843,290.111758 L256.254843,149.111758 Z M207.254843,149.111758 L180.254843,169.616983 L180.254843,286.775426 L207.254843,290.111758 L207.254843,149.111758 Z M284.280512,149.166601 L284.280512,258.166601 C361.40053,258.166601 366.547618,149.166601 284.280512,149.166601 Z M535.5,0 C600.393458,0 653,52.6065419 653,117.5 C653,182.393458 600.393458,235 535.5,235 C470.606542,235 418,182.393458 418,117.5 C418,52.6065419 470.606542,0 535.5,0 Z M535.5,14.6875 C478.718224,14.6875 432.6875,60.7182242 432.6875,117.5 C432.6875,174.281776 478.718224,220.3125 535.5,220.3125 C592.281776,220.3125 638.3125,174.281776 638.3125,117.5 C638.3125,60.7182242 592.281776,14.6875 535.5,14.6875 Z M543.25,146.493317 C548.045717,149.126505 551,153.992837 551,159.259213 C551,167.400325 544.060414,174 535.5,174 C526.939586,174 520,167.400325 520,159.259213 C520,153.992837 522.954283,149.126505 527.75,146.493317 C532.545717,143.860129 538.454283,143.860129 543.25,146.493317 Z M542.904212,58.028153 C547.453229,60.732357 549.982135,65.6671396 549.419,70.740787 L543.994,122.436727 C543.616106,126.647053 539.912274,129.881609 535.469,129.881609 C531.025726,129.881609 527.321894,126.647053 526.944,122.436727 L521.519,70.740787 C520.955865,65.6671396 523.484771,60.732357 528.033788,58.028153 C532.582804,55.323949 538.355196,55.323949 542.904212,58.028153 Z">
        </path>
    </svg>
</template>

<script setup>
import { computed } from 'vue';
import { useMainStore } from '@/store';

const store = useMainStore();
const isDarkMode = computed(() => store.isDarkMode);
const isMobile = computed(() => store.isMobile);
</script>