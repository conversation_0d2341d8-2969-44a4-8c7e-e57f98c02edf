<template>
  <!-- Whois Resolver -->
  <div class="whois-section my-4">
    <div class="text-secondary">
      <p>{{ t('whois.Note') }}</p>
    </div>
    <div class="row">
      <div class="col-12 mb-3">
        <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
          <div class="card-body">
            <div class="col-12 col-md-auto">
              <label for="queryURLorIP" class="col-form-label">{{ t('whois.Note2') }}</label>
            </div>

            <div class="input-group mb-2 mt-2 ">
              <input type="text" class="form-control" :class="{ 'dark-mode': isDarkMode }"
                     :disabled="whoisCheckStatus === 'running'" :placeholder="t('whois.Placeholder')"
                     v-model="queryURLorIP" @keyup.enter="onSubmit" name="queryURLorIP" id="queryURLorIP"
                     data-1p-ignore>

              <button class="btn btn-primary" @click="onSubmit"
                      :disabled="whoisCheckStatus === 'running' || !queryURLorIP">
                <span v-if="whoisCheckStatus === 'idle'">
                  {{
                                    t('whois.Run')
                  }}
                </span>
                <span v-if="whoisCheckStatus === 'running'" class="spinner-grow spinner-grow-sm"
                      aria-hidden="true"></span>
              </button>
            </div>

            <div class="jn-placeholder">
              <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
            </div>

            <!-- Results Table -->
            <div v-if="whoisResults && Object.keys(whoisResults).length">
              <div class="alert alert-success " v-if="!errorMsg">{{ t('whois.Note3') }}</div>
              <!-- Domain Results with Accordion -->
              <div v-if="type === 'domain' && providers.length > 0" class="accordion" id="whoisResultAccordion"
                   :data-bs-theme="isDarkMode ? 'dark' : ''">
                <div class="accordion-item" v-for="(providerDomain, index) in providers" :key="providerDomain">
                  <h2 class="accordion-header" :id="'heading' + index">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            :data-bs-target="'#collapse' + index"
                            :aria-expanded="index === 0 ? 'true' : 'false'"
                            :aria-controls="'collapse' + index" :class="{ collapsed: index !== 0 }">
                      <span>
                        <i class="bi" :class="'bi-' + (index + 1) + '-circle-fill'"></i>
                        <!-- Assuming providerDomain is the domain key from the API response -->
                        <strong>
                          {{ t('whois.Provider') }} : {{
 providerDomain.toUpperCase()
                          }}
                        </strong>
                      </span>
                    </button>
                  </h2>
                  <div :id="'collapse' + index" class="accordion-collapse collapse"
                       :class="{ show: index === 0 }" :aria-labelledby="'heading' + index">
                    <div class="accordion-body" :class="[isMobile ? ' p-2' : '']">
                      <div class="card card-body border-0 mt-3"
                           :class="[isDarkMode ? 'bg-black text-light' : 'bg-light']">
                        <!-- Accessing __raw data using the providerDomain as key -->
                        <pre v-if="whoisResults[providerDomain] && whoisResults[providerDomain].__raw">{{ filterDomainWhoisRawData(whoisResults[providerDomain].__raw) }}</pre>
                        <pre v-else>{{ t('whois.noRawData') }}</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- IP Results or Domain without multiple providers / different structure -->
              <div v-else-if="type === 'ip' && whoisResults.__raw" class="card card-body border-0 mt-3"
                   :class="[isDarkMode ? 'bg-black text-light' : 'bg-light']">
                <pre>{{ filterIPWhoisRawData(whoisResults.__raw) }}</pre>
              </div>
              <!-- Fallback for domain if structure is simple (direct __raw) or IP without __raw but has content -->
              <div v-else-if="whoisResults.__raw" class="card card-body border-0 mt-3"
                   :class="[isDarkMode ? 'bg-black text-light' : 'bg-light']">
                <pre>{{ type === 'domain' ? filterDomainWhoisRawData(whoisResults.__raw) : filterIPWhoisRawData(whoisResults.__raw) }}</pre>
              </div>
              <!-- If no specific data structure matched but results exist -->
              <div v-else-if="Object.keys(whoisResults).length > 0 && !errorMsg" class="card card-body border-0 mt-3"
                   :class="[isDarkMode ? 'bg-black text-light' : 'bg-light']">
                <pre>{{ JSON.stringify(whoisResults, null, 2) }}</pre>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { useMainStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import { trackEvent } from '@/utils/use-analytics';
  import { isValidIP } from '@/utils/valid-ip.js';

  const { t } = useI18n();

  const store = useMainStore();
  const isDarkMode = computed(() => store.isDarkMode);
  const isMobile = computed(() => store.isMobile);
  const isSignedIn = computed(() => store.isSignedIn);

  const queryURLorIP = ref('');
  const whoisCheckStatus = ref('idle'); // 'idle', 'running'
  const errorMsg = ref('');
  const providers = ref([]); // To store keys that represent domains if API returns multiple entries
  const type = ref(''); // 'domain' or 'ip'
  const whoisResults = ref({}); // Stores the WHOIS API response object

  // 检查 URL 输入是否有效并格式化域名
  const formatURL = (domainInput) => {
    let tempDomain = domainInput.trim();
    // 移除末尾的斜杠
    if (tempDomain.endsWith('/')) {
      tempDomain = tempDomain.slice(0, -1);
    }

    // 检查是否包含协议头，若没有则尝试为其添加 http:// 以便进行 URL 格式验证
    if (!tempDomain.match(/^https?:\/\//) && !isValidIP(tempDomain)) { // Don't add http to IP addresses
      tempDomain = 'http://' + tempDomain;
    }

    try {
      const url = new URL(tempDomain);
      let hostname = url.hostname;

      // 再次确保主机名末尾没有斜杠 (URL object might handle some cases)
      if (hostname.endsWith('/')) {
        hostname = hostname.slice(0, -1);
      }

      // 提取主域名 (e.g., example.com from sub.example.com) - 您之前的逻辑
      // 如果 API 需要 FQDN (如 sub.example.com)，则不应执行此操作。
      // 假设 API 可以处理 FQDN，我们将返回完整的 hostname
      // const parts = hostname.split('.');
      // const mainDomain = parts.length > 1 ? parts.slice(-2).join('.') : hostname;

      // Regex for basic domain validation (can be more complex)
      if (hostname.match(/^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,63}$/i)) {
        return hostname; // 返回完整的主机名
      }
    } catch (e) {
      // console.warn("Error parsing URL in formatURL:", e);
    }
    return false;
  };

  // 检查输入是否有效
  const validInput = (input) => {
    const cleanedInput = input.trim();
    if (!cleanedInput) {
      errorMsg.value = t('whois.emptyInput');
      return false;
    }

    const formattedDomain = formatURL(cleanedInput);

    if (formattedDomain) {
      type.value = 'domain';
      return formattedDomain;
    } else if (isValidIP(cleanedInput)) {
      type.value = 'ip';
      // 对于 IP，也移除末尾斜杠 (虽然不常见)
      return cleanedInput.endsWith('/') ? cleanedInput.slice(0, -1) : cleanedInput;
    } else {
      errorMsg.value = t('whois.invalidURL');
      return false;
    }
  };

  // 提交查询
  const onSubmit = () => {
    trackEvent('Section', 'StartClick', 'Whois');
    errorMsg.value = '';
    providers.value = [];
    whoisResults.value = {};
    type.value = ''; // Reset type

    const query = validInput(queryURLorIP.value);
    if (query) {
      getWhoisResults(query);
    }
  };

  // 获取 Whois 结果
  const getWhoisResults = async (query) => {
    whoisCheckStatus.value = 'running';
    let apiUrl = '';

    // *** MODIFICATION: Use the new API endpoint and structure query param based on type ***
    // 假设 v2.xxapi.cn 对域名和 IP 使用相同的 'domain' 参数
    // 如果 IP 有不同参数名（例如 'ip'），需要调整
    const baseApiUrl = 'https://v2.xxapi.cn/api/whois';
    apiUrl = `${baseApiUrl}?domain=${encodeURIComponent(query)}`;
    // 如果IP有特定参数:
    // if (type.value === 'domain') {
    //     apiUrl = `${baseApiUrl}?domain=${encodeURIComponent(query)}`;
    // } else if (type.value === 'ip') {
    //     apiUrl = `${baseApiUrl}?ip=${encodeURIComponent(query)}`; // Or whatever param it uses
    // } else {
    //     errorMsg.value = "Invalid query type for API construction.";
    //     whoisCheckStatus.value = 'idle';
    //     return;
    // }


    try {
      const response = await fetch(apiUrl);
      // 尝试解析JSON，即使响应不OK，因为API可能在错误时也返回JSON
      const data = await response.json().catch(e => {

        throw new Error(t('whois.apiErrorParse')); // Specific error for JSON parsing failure
      });

      if (!response.ok) {
        // 如果API在错误时返回了结构化的错误信息
        if (data && (data.error || data.message || data.msg)) {
          throw new Error(data.error || data.message || data.msg);
        }
        throw new Error(`${t('whois.networkError')} ${response.status} ${response.statusText}`);
      }

      // 检查API返回的数据中是否有错误指示 (一些API可能在200 OK时返回逻辑错误)
      if (data && (data.error || data.code && data.code !== 0 && data.code !== 200)) { // Adjust success codes as needed
        errorMsg.value = data.error || data.message || data.msg || t('whois.apiReturnedError');
        whoisResults.value = {}; // Clear results on error
      } else {
        whoisResults.value = data;
        // *** MODIFICATION: Call getProviders after successfully fetching and parsing data ***
        getProviders(data); // This will populate providers.value if applicable
        errorMsg.value = ''; // Clear error message on success

        // 成就检查 - 保持不变
        if (type.value === 'domain' && isSignedIn.value && query.toLowerCase().includes('ipcheck.ing')) {
          checkAchievements();
        }
      }

    } catch (error) {
      console.error('Error fetching Whois results:', error);
      errorMsg.value = error.message || t('whois.fetchError');
      whoisResults.value = {}; // Clear results on fetch error
    } finally {
      whoisCheckStatus.value = 'idle';
    }
  };

  // 获取 Whois 服务商 (或者说，从API响应中提取可用的域名键)
  const getProviders = (data) => {
    providers.value = []; // Reset providers
    if (type.value === 'domain' && data && typeof data === 'object') {
      for (const key in data) {
        // 假设API对每个查询的域名返回一个对象，键是域名本身，值是WHOIS数据
        // 并且这个WHOIS数据对象包含一个'__raw'字段
        // 这是基于您之前代码的假设，您可能需要根据 v2.xxapi.cn 的实际响应调整
        if (Object.prototype.hasOwnProperty.call(data, key) &&
          typeof data[key] === 'object' &&
          data[key] !== null &&
          Object.prototype.hasOwnProperty.call(data[key], '__raw')) {
          // 检查key是否像一个域名，防止将如 "status", "error" 等字段误认为provider
          if (key.includes('.')) { // Simple check for a dot, common in domains
            providers.value.push(key);
          }
        }
      }
      // 如果API直接在顶层返回 __raw (例如对单个域名查询)，且没有以域名为键的结构
      if (providers.value.length === 0 && data.__raw && queryURLorIP.value) {
        // 尝试将当前查询的域名视为 "provider" 以显示
        const currentQueryDomain = formatURL(queryURLorIP.value.trim());
        if (currentQueryDomain) {
          // providers.value.push(currentQueryDomain);
          // In this case, the accordion might not be needed, or whoisResults itself is the data for the single provider
          // For simplicity, if only __raw at top level for domain, we might not need the accordion.
          // The template already has a fallback for `whoisResults.__raw`.
        }
      }

    }
    // For IP, we usually expect a direct object, possibly with __raw.
    // The template already handles `v-else-if="type === 'ip' && whoisResults.__raw"`
  };


  const filterDomainWhoisRawData = (text) => {
    if (typeof text !== 'string') return t('whois.noRawData');
    text = text.replace(/^( {1,20})/gm, '');
    const cutoffIndex = text.indexOf('\nFor more information');
    return cutoffIndex !== -1 ? text.substring(0, cutoffIndex) : text;
  };

  const filterIPWhoisRawData = (text) => {
    if (typeof text !== 'string') return t('whois.noRawData');
    text = text.replace(/^#.*\n/gm, '');
    text = text.replace(/^\n*/, '');
    text = text.replace(/\n$/, '');
    return text;
  };

  const checkAchievements = () => {
    if (!store.userAchievements.CuriousCat.achieved) {
      store.setTriggerUpdateAchievements('CuriousCat');
    }
  };

</script>

<style scoped>
  .jn-placeholder {
    height: 16pt;
  }
</style>