<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="MyIP,IP Tool,My IP,IP check,IP lookup,DNS leak test,WebRTC test,Speed test,DNS lookup,Whois lookup, Ping test, privacy test">
  <meta name="description"
    content="A better and open source IP Toolbox. Easy to check what's your IPs, IP information, check for DNS leaks, examine WebRTC connections, test website availability, lookup DNS record, lookup Whois, and test latency from around the world.">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="theme-color" content="#f8f9fa">
  <meta name="background-color" content="#ffffff">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="apple-touch-icon" href="logos/ios-logo-512.png">
  <title>𝓌𝑜𝒷 MyIP | 查看我的 IP 地址 - 查询本机 IP 地址及归属地 - 查看 WebRTC 连接 IP - DNS 泄露检测 - 网速测试 </title>
  <link rel="icon" href="logos/logo.svg">
  <link rel="icon" type="image/svg+xml" href="favicon.svg">

  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>

  <style>
    .jn-loading {
      margin-top: 30vh;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .jn-spinner-text {
      margin-top: 20px;
      text-align: center;
    }

    .jn-spinner-text p {
      font-size: 14px;
      color: #777;
    }

    /* 炫酷的DNA螺旋加载动画 */
    .dna-spinner {
      position: relative;
      width: 60px;
      height: 60px;
      margin: 20px auto;
    }

    .dna-strand {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 3px solid transparent;
    }

    .dna-strand:nth-child(1) {
      border-top: 3px solid #ff6b6b;
      border-right: 3px solid #ff6b6b;
      animation: dna-rotate 1.5s linear infinite;
    }

    .dna-strand:nth-child(2) {
      border-bottom: 3px solid #4ecdc4;
      border-left: 3px solid #4ecdc4;
      animation: dna-rotate 1.5s linear infinite reverse;
    }

    .dna-strand:nth-child(3) {
      border-top: 3px solid #45b7d1;
      border-left: 3px solid #45b7d1;
      animation: dna-rotate 2s linear infinite;
      transform: scale(0.8);
    }

    @keyframes dna-rotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    /* 粒子效果 */
    .particle-container {
      position: relative;
      width: 100px;
      height: 100px;
      margin: 10px auto;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      border-radius: 50%;
      animation: particle-float 3s infinite ease-in-out;
    }

    .particle:nth-child(1) {
      top: 20%;
      left: 20%;
      animation-delay: 0s;
    }

    .particle:nth-child(2) {
      top: 20%;
      right: 20%;
      animation-delay: 0.5s;
    }

    .particle:nth-child(3) {
      bottom: 20%;
      left: 20%;
      animation-delay: 1s;
    }

    .particle:nth-child(4) {
      bottom: 20%;
      right: 20%;
      animation-delay: 1.5s;
    }

    .particle:nth-child(5) {
      top: 50%;
      left: 50%;
      animation-delay: 2s;
      transform: translate(-50%, -50%);
    }

    @keyframes particle-float {
      0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
      }
      50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
      }
    }

    /* 脉冲波纹效果 */
    .pulse-container {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 15px auto;
    }

    .pulse-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      border: 2px solid #ff6b6b;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: pulse-expand 2s infinite ease-out;
    }

    .pulse-ring:nth-child(2) {
      animation-delay: 0.5s;
      border-color: #4ecdc4;
    }

    .pulse-ring:nth-child(3) {
      animation-delay: 1s;
      border-color: #45b7d1;
    }

    .pulse-ring:nth-child(4) {
      animation-delay: 1.5s;
      border-color: #ffd93d;
    }

    @keyframes pulse-expand {
      0% {
        width: 20px;
        height: 20px;
        opacity: 1;
      }
      100% {
        width: 80px;
        height: 80px;
        opacity: 0;
      }
    }

    /* 组合动画容器 */
    .cool-loader {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }
  </style>
</head>

<body>
  <div id="jn-loading" class="jn-loading">
    <div>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 60" width="120" height="60">
        <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ff6b6b" />
                <stop offset="100%" style="stop-color:#4ecdc4" />
            </linearGradient>
        </defs>

        <!-- 背景矩形 -->
        <rect x="0" y="0" width="120" height="60" fill="#f7f7f7" rx="10" ry="10" />

        <!-- 渐变文字 -->
        <text x="60" y="35" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="url(#gradient)">
            MyIP
        </text>

        <!-- 动画圆形1 -->
        <circle cx="35" cy="45" r="5" fill="#ff6b6b">
            <animate attributeName="cy" values="45;40;45" dur="1s" repeatCount="indefinite" />
        </circle>

        <!-- 动画圆形2 -->
        <circle cx="85" cy="20" r="5" fill="#4ecdc4">
            <animate attributeName="cy" values="20;25;20" dur="1s" repeatCount="indefinite" />
        </circle>
    </svg>
</div>

    <div class="jn-spinner-text">
      <p>It may take a while for the first time, please wait...</p>
      <p>首次加载的时间可能会较长，请等待...</p>
    </div>

    <!-- 炫酷的组合加载动画 -->
    <div class="cool-loader">
      <!-- DNA螺旋动画 -->
      <div class="dna-spinner">
        <div class="dna-strand"></div>
        <div class="dna-strand"></div>
        <div class="dna-strand"></div>
      </div>

      <!-- 粒子浮动效果 -->
      <div class="particle-container">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
      </div>

      <!-- 脉冲波纹效果 -->
      <div class="pulse-container">
        <div class="pulse-ring"></div>
        <div class="pulse-ring"></div>
        <div class="pulse-ring"></div>
        <div class="pulse-ring"></div>
      </div>
    </div>
  </div>
  <div id="app"></div>

  <!-- Leaflet JavaScript -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>

  <script type="module" src="/frontend/main.js"></script>
</body>

</html>
