import { isValidIP } from '@/utils/valid-ip.js';
import { getIPFromIpify_V6 } from "./ipify-v6";

// 从 IPCheck.ing 获取 IPv6 地址
const getIPFromIPChecking6 = async (originalSite) => {
    const source = "𝓌𝑜𝒷IP IPv6";

    // 首先尝试通过本地 API 获取 IP（如果返回的是 IPv6）
    try {
        const response = await fetch('/api/ip-services?service=ipwho');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip) && data.ip.includes(':')) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
            // 如果 API 返回的是 IPv4 地址，说明网络不支持 IPv6
            if (data.ip && isValidIP(data.ip) && !data.ip.includes(':')) {
                return {
                    ip: null,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试原始 IPCheck.ing IPv6 API
    try {
        let ip;
        originalSite ? ip = await getFromJson() : ip = await getFromTrace();
        // 严格验证：必须是有效的 IPv6 地址（包含冒号且不是 IPv4）
        if (isValidIP(ip) && ip.includes(':') && !ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
            return {
                ip: ip,
                source: source
            };
        }
    } catch (error) {
        // 继续到故障转移
    }

    // 最后的故障转移
    const { ip, source: fallbackSource } = await getIPFromIpify_V6();
    return {
        ip: ip,
        source: fallbackSource
    };
};

const getFromJson = async () => {
    try {
        const response = await fetch("https://6.ipcheck.ing");
        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data = await response.json();
        const ip = data.ip;
        return ip;
    } catch (error) {

    }
    return getFromTrace();
};

const getFromTrace = async () => {
    try {
        const response = await fetch("https://6.ipcheck.ing/cdn-cgi/trace");
        const data = await response.text();
        const lines = data.split("\n");
        const ipLine = lines.find((line) => line.startsWith("ip="));
        let ip = "";
        if (ipLine) {
            ip = ipLine.split("=")[1];
        }
        return ip;
    } catch (error) {

        throw error;
    }
};

export { getIPFromIPChecking6 };