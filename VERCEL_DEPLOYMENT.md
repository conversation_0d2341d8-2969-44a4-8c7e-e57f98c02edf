# Vercel部署说明

## 环境变量配置

在Vercel Dashboard中配置以下环境变量：

### 必需的环境变量
```
ALLOWED_REFERER_DOMAINS=wobip.pages.dev,myip.wobys.dpdns.org,localhost,127.0.0.1
CLOUDFLARE_API=****************************************
IP2LOCATION_API_KEY=********************************
IPAPIIS_API_KEY=90102d59fc571d38
IPINFO_API_TOKEN=ba341d1d251034
MAC_LOOKUP_API_KEY=01jt2656q3xdy4n8az07t646g701jt267459gkny5vxqh2rb0qwcw92bsv0vqlou
MAXMIND_LICENSE_KEY=****************************************
MAXMIND_ACCOUNT_ID=1174129
```

### 可选的环境变量
```
MEITUAN_APP_KEY=yourAppKey
IPGEOLOCATION_API_KEY=your_ipgeolocation_key_here
IPCHECKING_API_KEY=your_ipchecking_key_here
IPCHECKING_API_ENDPOINT=your_ipchecking_endpoint_here
GOOGLE_MAP_API_KEY=your_google_maps_key_here
```

## 部署步骤

1. 确保所有环境变量在Vercel Dashboard中已配置
2. 推送代码到GitHub
3. Vercel会自动构建和部署

## API路由

- 生产环境使用: `/api/ip-services?service=SERVICE_NAME&ip=IP_ADDRESS`
- 开发环境使用: `http://localhost:PORT/api/ip-services?service=SERVICE_NAME&ip=IP_ADDRESS`

## 支持的服务

- `ipapi-com` - IP-API.com (免费，无需API key)
- `ip-sb` - IP.SB (免费，无需API key)
- `meituan` - 美团IP (免费，无需API key)
- `ipinfo` - IPInfo.io (免费版本，有API token更好)
- `ipapi-is` - IPAPI.is (需要API key)
- `ip2location` - IP2Location (需要API key)
- `ipgeolocation` - IPGeolocation.io (需要API key，免费30,000 requests/月)
- `ipwho` - IPWho.is (完全免费，无需API key)
- `maxmind` - MaxMind (需要账户ID和许可证)
- `ipcheck` - IPCheck.ing (需要API key和endpoint)

## 故障排除

如果API返回"Unknown"数据：
1. 检查环境变量是否正确配置
2. 检查API密钥是否有效
3. 查看Vercel函数日志
4. 确认API服务商没有请求限制
