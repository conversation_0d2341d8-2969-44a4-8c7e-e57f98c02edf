import { isValidIP } from '@/utils/valid-ip.js';

// 从 Cloudflare 获取 IPv6 地址
const getIPFromCloudflare_V6 = async () => {
    const source = "Cloudflare IPv6";

    // 首先尝试通过您的 API 获取 IP（如果返回的是 IPv6）
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip) && data.ip.includes(':') && !data.ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
            // 如果 API 返回的是 IPv4 地址，说明网络不支持 IPv6
            if (data.ip && isValidIP(data.ip) && !data.ip.includes(':')) {
                // 网络环境不支持 IPv6，直接返回失败
                return {
                    ip: null,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试多个 IPv6 端点
    const endpoints = [
        "https://[2606:4700:4700::1111]/cdn-cgi/trace",
        "https://[2606:4700:4700::1001]/cdn-cgi/trace",
        "https://cloudflare.com/cdn-cgi/trace", // 这个会自动选择协议
        "https://www.cloudflare.com/cdn-cgi/trace",
        // 备用 IPv6 端点
        "https://ipv6.icanhazip.com",
        "https://api6.ipify.org",
        "https://v6.ident.me"
    ];

    for (const endpoint of endpoints) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(endpoint, {
                signal: controller.signal,
                headers: {
                    'Accept': 'text/plain'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) continue;

            const data = await response.text();
            let ip = '';

            // 处理不同格式的响应
            if (endpoint.includes('cdn-cgi/trace')) {
                const lines = data.split("\n");
                const ipLine = lines.find((line) => line.startsWith("ip="));
                if (ipLine) {
                    ip = ipLine.split("=")[1].trim();
                }
            } else {
                // 对于其他端点，直接使用响应文本作为IP
                ip = data.trim();
            }

            // 检查是否为有效的 IPv6 地址（严格验证）
            if (isValidIP(ip) && ip.includes(':') && !ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
                return {
                    ip: ip,
                    source: source
                };
            }
        } catch (error) {
            // 继续尝试下一个端点
            continue;
        }
    }

    // 所有端点都失败，返回空结果
    return {
        ip: null,
        source: source
    };
};

export { getIPFromCloudflare_V6 };