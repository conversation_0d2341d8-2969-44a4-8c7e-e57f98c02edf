<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .api-test {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .api-test h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
        }
        .result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>API接口测试页面</h1>
    <p>测试IP: <input type="text" id="testIP" value="*******" placeholder="输入要测试的IP地址"></p>
    <button onclick="testAllAPIs()">测试所有API</button>
    <button onclick="clearResults()">清除结果</button>

    <div id="apiTests">
        <div class="api-test" id="test-ipapi-com">
            <h3>IP-API.com</h3>
            <button onclick="testAPI('ipapi-com')">测试</button>
            <div class="status loading" id="status-ipapi-com">未测试</div>
            <div class="result" id="result-ipapi-com"></div>
        </div>

        <div class="api-test" id="test-ipinfo">
            <h3>IPInfo.io</h3>
            <button onclick="testAPI('ipinfo')">测试</button>
            <div class="status loading" id="status-ipinfo">未测试</div>
            <div class="result" id="result-ipinfo"></div>
        </div>

        <div class="api-test" id="test-ip-sb">
            <h3>IP.SB</h3>
            <button onclick="testAPI('ip-sb')">测试</button>
            <div class="status loading" id="status-ip-sb">未测试</div>
            <div class="result" id="result-ip-sb"></div>
        </div>

        <div class="api-test" id="test-meituan">
            <h3>美团IP</h3>
            <button onclick="testAPI('meituan')">测试</button>
            <div class="status loading" id="status-meituan">未测试</div>
            <div class="result" id="result-meituan"></div>
        </div>

        <div class="api-test" id="test-ipgeolocation">
            <h3>IPGeolocation.io</h3>
            <button onclick="testAPI('ipgeolocation')">测试</button>
            <div class="status loading" id="status-ipgeolocation">未测试</div>
            <div class="result" id="result-ipgeolocation"></div>
        </div>

        <div class="api-test" id="test-ipwho">
            <h3>IPWho.is</h3>
            <button onclick="testAPI('ipwho')">测试</button>
            <div class="status loading" id="status-ipwho">未测试</div>
            <div class="result" id="result-ipwho"></div>
        </div>

        <div class="api-test" id="test-ip2location">
            <h3>IP2Location</h3>
            <button onclick="testAPI('ip2location')">测试</button>
            <div class="status loading" id="status-ip2location">未测试</div>
            <div class="result" id="result-ip2location"></div>
        </div>

        <div class="api-test" id="test-ipapi-is">
            <h3>IPAPI.is</h3>
            <button onclick="testAPI('ipapi-is')">测试</button>
            <div class="status loading" id="status-ipapi-is">未测试</div>
            <div class="result" id="result-ipapi-is"></div>
        </div>

        <div class="api-test" id="test-maxmind">
            <h3>MaxMind</h3>
            <button onclick="testAPI('maxmind')">测试</button>
            <div class="status loading" id="status-maxmind">未测试</div>
            <div class="result" id="result-maxmind"></div>
        </div>

        <div class="api-test" id="test-ipcheck">
            <h3>IPCheck.ing</h3>
            <button onclick="testAPI('ipcheck')">测试</button>
            <div class="status loading" id="status-ipcheck">未测试</div>
            <div class="result" id="result-ipcheck"></div>
        </div>
    </div>

    <script>
        async function testAPI(service) {
            const ip = document.getElementById('testIP').value || '*******';
            const statusEl = document.getElementById(`status-${service}`);
            const resultEl = document.getElementById(`result-${service}`);
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.textContent = '';
            
            try {
                const response = await fetch(`/api/ip-services?service=${service}&ip=${ip}`);
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.textContent = '成功';
                    statusEl.className = 'status success';
                    resultEl.textContent = JSON.stringify(data, null, 2);
                } else {
                    statusEl.textContent = '失败';
                    statusEl.className = 'status error';
                    resultEl.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                statusEl.textContent = '错误';
                statusEl.className = 'status error';
                resultEl.textContent = `错误: ${error.message}`;
            }
        }

        async function testAllAPIs() {
            const services = ['ipapi-com', 'ipinfo', 'ip-sb', 'meituan', 'ipgeolocation', 'ipwho', 'ip2location', 'ipapi-is', 'maxmind', 'ipcheck'];

            for (const service of services) {
                await testAPI(service);
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        function clearResults() {
            const services = ['ipapi-com', 'ipinfo', 'ip-sb', 'meituan', 'ipgeolocation', 'ipwho', 'ip2location', 'ipapi-is', 'maxmind', 'ipcheck'];
            
            services.forEach(service => {
                const statusEl = document.getElementById(`status-${service}`);
                const resultEl = document.getElementById(`result-${service}`);
                statusEl.textContent = '未测试';
                statusEl.className = 'status loading';
                resultEl.textContent = '';
            });
        }
    </script>
</body>
</html>
