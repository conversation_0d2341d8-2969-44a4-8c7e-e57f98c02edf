// 合并的用户相关API
// import { refererMiddleware } from '../middleware/referer-middleware.js';

// 简化的referer检查中间件
const refererMiddleware = (req, res, next) => {
  // 在Vercel环境中简化referer检查
  next();
};

// 获取用户信息
const getUserInfoHandler = async (req, res) => {
  try {
    // 基本用户信息
    const userInfo = {
      id: 'anonymous',
      name: 'Anonymous User',
      email: null,
      avatar: null,
      isSignedIn: false,
      preferences: {
        theme: 'auto',
        language: 'zh-CN'
      },
      achievements: [],
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    res.json(userInfo);
  } catch (error) {

    res.status(500).json({ error: 'Failed to get user information' });
  }
};

// 更新用户成就
const updateAchievementHandler = async (req, res) => {
  try {
    const { achievement, userId = 'anonymous' } = req.body;
    
    if (!achievement) {
      return res.status(400).json({ error: 'Achievement parameter is required' });
    }

    // 在实际应用中，这里会更新数据库
    // 现在只是返回成功响应
    const updatedAchievement = {
      id: achievement,
      userId: userId,
      unlockedAt: new Date().toISOString(),
      title: `Achievement: ${achievement}`,
      description: `User unlocked ${achievement}`,
      points: 10
    };

    res.json({
      success: true,
      achievement: updatedAchievement,
      message: 'Achievement updated successfully'
    });
  } catch (error) {
    console.error('Error updating achievement:', error);
    res.status(500).json({ error: 'Failed to update achievement' });
  }
};

// 获取配置信息
const getConfigsHandler = async (req, res) => {
  try {
    const configs = {
      originalSite: true,
      enablePingTest: true,
      enableMTRTest: true,
      enableDNSResolver: true,
      enableWhois: true,
      enableMacChecker: true,
      enableBrowserInfo: true,
      enableSecurityChecklist: true,
      enableInvisibilityTest: true,
      enableCensorshipCheck: true,
      enableRuleTest: true,
      maxPingServers: 20,
      pingTimeout: 5000,
      mtrTimeout: 30000,
      dnsTimeout: 5000,
      whoisTimeout: 10000,
      version: '5.0.0',
      buildTime: new Date().toISOString()
    };

    res.json(configs);
  } catch (error) {

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get configurations'
    });
  }
};

// 获取真实IP (用于WebRTC测试)
const getRealIPHandler = async (req, res) => {
  try {
    // 获取客户端真实IP
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = forwarded ? forwarded.split(',')[0].trim() : 
                   req.headers['x-real-ip'] || 
                   req.connection?.remoteAddress || 
                   req.socket?.remoteAddress ||
                   req.ip;

    // 如果是本地地址，在开发环境中返回模拟的真实IP
    if (!realIP || realIP === '::1' || realIP === '127.0.0.1' || realIP.startsWith('192.168.') || realIP.startsWith('10.')) {

      const simulatedRealIP = '**************';
      return res.json({
        realIP: simulatedRealIP,
        message: 'Simulated real IP for development environment',
        detectedIP: realIP,
        isSimulated: true,
        realCountry: '中国',
        realCountryCode: 'CN'
      });
    }

    console.log('Real IP for detection:', realIP);
    res.json({ realIP: realIP });

  } catch (error) {
    console.error('Error getting real IP:', error);
    res.status(500).json({ error: 'Failed to get real IP' });
  }
};

// 主处理函数
export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  refererMiddleware(req, res, async () => {
    const { action } = req.query;
    
    switch (action) {
      case 'info':
        return getUserInfoHandler(req, res);
      case 'achievement':
        return updateAchievementHandler(req, res);
      case 'configs':
        return getConfigsHandler(req, res);
      case 'real-ip':
        return getRealIPHandler(req, res);
      default:
        res.status(400).json({ error: 'Invalid action parameter' });
    }
  });
}
