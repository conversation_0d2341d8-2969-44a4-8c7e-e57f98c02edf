function maskedInfo(t) {
  const fakecard = {};

    fakecard.ipv4 = "*******",
    fakecard.ipv6 = "2001:4860:4860::8888",
    fakecard.dnsendpoints = "***********",
    fakecard.webrtcip = "***************",
    fakecard.country_name = "United States";
    fakecard.country_code = "US";
    fakecard.region = "California";
    fakecard.city = "Mountain View";
    fakecard.latitude = "37.40599";
    fakecard.longitude = "-122.078514";
    fakecard.isp = "Google LLC";
    fakecard.asn = "AS888888";
    fakecard.asnlink = "https://radar.cloudflare.com/AS15169",
    fakecard.mapUrl = '/res/defaultMap.webp';
    fakecard.mapUrl_dark = '/res/defaultMap_dark.webp';
    fakecard.showASNInfo = false;
    fakecard.isProxy = t('ipInfos.proxyDetect.no');
    fakecard.type = t('ipInfos.proxyDetect.type.Business');
    fakecard.qualityScore = 100;
    fakecard.proxyProtocol = t('ipInfos.proxyDetect.unknownProtocol');
    fakecard.proxyOperator = "unknown";

  return fakecard;
}

export { maskedInfo };