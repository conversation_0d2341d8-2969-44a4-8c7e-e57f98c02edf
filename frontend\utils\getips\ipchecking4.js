import { isValidIP } from '@/utils/valid-ip.js';
import { getIPFromIpify_V4 } from "./ipify-v4";

// 从 IPCheck.ing 获取 IPv4 地址
const getIPFromIPChecking4 = async (originalSite) => {
    const source = "𝓌𝑜𝒷IP IPv4";

    // 首先尝试通过您的快速 API 获取 IP
    try {
        const response = await fetch('https://myip.wobys.dpdns.org/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip) && !data.ip.includes(':')) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试原始 IPCheck.ing IPv4 API
    try {
        let ip;
        originalSite ? ip = await getFromJson() : ip = await getFromTrace();
        if (isValidIP(ip) && !ip.includes(':')) {
            return {
                ip: ip,
                source: source
            };
        }
    } catch (error) {
        // 继续到故障转移
    }

    // 最后的故障转移
    const { ip, source: fallbackSource } = await getIPFromIpify_V4();
    return {
        ip: ip,
        source: fallbackSource
    };
};

const getFromJson = async () => {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        const response = await fetch("https://4.ipcheck.ing", {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data = await response.json();
        const ip = data.ip;
        return ip;
    } catch (error) {
        // 如果 JSON API 失败，尝试 trace API
        return getFromTrace();
    }
};

const getFromTrace = async () => {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

        const response = await fetch("https://4.ipcheck.ing/cdn-cgi/trace", {
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data = await response.text();
        const lines = data.split("\n");
        const ipLine = lines.find((line) => line.startsWith("ip="));
        let ip = "";
        if (ipLine) {
            ip = ipLine.split("=")[1].trim();
        }
        return ip;
    } catch (error) {
        throw error;
    }
};

export { getIPFromIPChecking4 };