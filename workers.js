// Cloudflare Workers主文件 我的自定义域名：https://myip.wobys.dpdns.org/
// 环境变量通过 wrangler.toml 或 Cloudflare Dashboard 设置

// 通用工具函数
class Utils {
  // 清理IP地址中的端口号
  static cleanIPAddress(ip) {
    if (!ip) return ip;

    // 对于IPv6地址，移除末尾的端口号
    if (ip.includes(':')) {
      // 检查是否有端口号（最后一个冒号后面是数字）
      const lastColonIndex = ip.lastIndexOf(':');
      const afterLastColon = ip.substring(lastColonIndex + 1);

      // 如果最后一个冒号后面是纯数字且不是IPv6地址的一部分，则移除它
      if (/^\d+$/.test(afterLastColon) && lastColonIndex > ip.indexOf('::')) {
        return ip.substring(0, lastColonIndex);
      }
    }

    return ip;
  }

  // 验证 IP 地址
  static isValidIP(ip) {
    if (!ip) return false; // Add null/undefined check

    // 先清理IP地址
    const cleanedIP = Utils.cleanIPAddress(ip);

    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // 更完整的IPv6正则表达式
    const ipv6Regex =
      /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

    return ipv4Regex.test(cleanedIP) || ipv6Regex.test(cleanedIP);
  }

  // 验证域名
  static isValidDomain(domain) {
    if (!domain) return false;
    // 允许国际化域名 (IDN) - 更宽松的模式，但对于 DoH 查询通常可以
    // 更严格的模式可能是：/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i;
    // 这个模式允许 unicode 字符在域名部分，但不检查顶级域名 (TLD) 的有效性
    const domainPattern = /^(([^.\s\\\/:\(\)\[\]@<>",;]+)\.)+([^.\s\\\/:\(\)\[\]@<>",;]{2,})$/i; 
    return domainPattern.test(domain);
  }

  // 验证 ASN
  static isValidASN(asn) {
    if (!asn) return false;
    return /^[0-9]+$/.test(asn);
  }

  // 验证 MAC 地址 (已修复语法错误)
  static isValidMAC(address) {
    if (!address) return false;
    const normalizedAddress = address.replace(/[:-]/g, '');
    return normalizedAddress.length >= 6 && normalizedAddress.length <= 12 && /^[0-9A-Fa-f]+$/.test(normalizedAddress);
  }

  // 验证用户ID
  static isValidUserID(userID) {
    if (typeof userID !== 'string') return false;
    if (userID.length !== 28 || !/^[a-zA-Z0-9]+$/.test(userID)) return false;
    return true;
  }

  // Referer 检查 (保持用户提供的原始逻辑，包含硬编码域名)
  // static refererCheck(referer, allowedDomains = []) { // <-- 这个 allowedDomains 才是实际接收的参数
  //   if (!referer) return false;
  //   try {
  //     const url = new URL(referer);
  //     const hostname = url.hostname;
  //     const defaultAllowed = ['ip.wobshare.us.kg', 'wobip.pages.dev'];
  //     // 更新为项目实际使用的域名
  //     const allAllowed = defaultAllowed.concat(allowedDomains);
      
  //     // 添加调试日志，确认 allAllowed 的最终内容
  //     console.log(`[RefererCheck Debug] Final Allowed Domains (in function): ${JSON.stringify(allAllowed)}`);
  //     console.log(`[RefererCheck Debug] Incoming Hostname to check: "${hostname}"`);

  //     return allAllowed.some(domain => hostname === domain || hostname.endsWith('.' + domain));
  //   } catch {
  //     console.error(`[RefererCheck Error] Failed to parse referer: ${referer}`);
  //     return false;
  //   }
  // }

  // 修改后的 refererCheck 函数，允许所有来源
  static refererCheck(referer, allowedDomains = []) {
    // 始终返回 true，以禁用 Referer 检查并允许所有请求
    // 这将忽略任何传入的 referer 或 allowedDomains 参数。
    return true;
  }


  // 错误响应
  static errorResponse(message, status = 400) {
    return new Response(JSON.stringify({ error: message }), {
      status,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*' // Added for errors too
      }
    });
  }

  // 成功响应
  static jsonResponse(data, status = 200) {
    return new Response(JSON.stringify(data), {
      status,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*' // Added for success too
      }
    });
  }

  // 获取随机 API Key
  static getRandomKey(keys) {
    if (!keys) return null;
    const keyArray = keys.split(',');
    return keyArray[Math.floor(Math.random() * keyArray.length)].trim(); // Added trim
  }

  //正常化查询参数:删除尾随斜杠(如果有)
  static normalizeQueryParam(paramValue) {
    if (typeof paramValue === 'string' && paramValue.endsWith('/')) {
      return paramValue.slice(0, -1);
    }
    return paramValue;
  }
}

// API 处理器类
class APIHandlers {
  constructor(env) {
    this.env = env;
  }

  // 获取客户端 IP 地址
  getClientIP(request) {
    let ip = request.headers.get('CF-Connecting-IP') ||
             request.headers.get('x-real-ip') ||
             request.headers.get('x-forwarded-for')?.split(',')[0].trim() ||
             null;

    if (ip) {
      // 清理IPv6地址中的端口号
      ip = Utils.cleanIPAddress(ip);
      if (Utils.isValidIP(ip)) {
        return ip;
      }
    }
    return null;
  }

  // Cloudflare Radar API
  async handleCloudflareRadar(request) {
    const url = new URL(request.url);
    let asn = Utils.normalizeQueryParam(url.searchParams.get('asn'));
    
    if (!asn) {
      return Utils.errorResponse('No ASN provided');
    }
    
    if (!Utils.isValidASN(asn)) {
      return Utils.errorResponse('Invalid ASN');
    }

    try {
      const cfApiKey = this.env.CLOUDFLARE_API;
      if (!cfApiKey) return Utils.errorResponse('Cloudflare API key not configured', 500);

      const headers = {
        'Authorization': `Bearer ${cfApiKey}`,
        'Content-Type': 'application/json'
      };

      const endpoints = [
        `/radar/entities/asns/${asn}`,
        `/radar/http/summary/ip_version?asn=${asn}&dateRange=7d`,
        `/radar/http/summary/http_protocol?asn=${asn}&dateRange=7d`,
        `/radar/http/summary/device_type?asn=${asn}&dateRange=7d`,
        `/radar/http/summary/bot_class?asn=${asn}&dateRange=7d`
      ];

      const promises = endpoints.map(endpoint => 
        fetch(`https://api.cloudflare.com/client/v4${endpoint}`, { headers })
          .then(res => {
            if (!res.ok) throw new Error(`Cloudflare API Error for ${endpoint}: ${res.status}`);
            return res.json();
          })
      );

      const [asnInfo, ipVersion, httpProtocol, deviceType, botType] = await Promise.all(promises);

      const cleanedData = {
        asnName: asnInfo.result?.asn?.name,
        asnOrgName: asnInfo.result?.asn?.orgName,
        estimatedUsers: asnInfo.result?.asn?.estimatedUsers?.estimatedUsers,
        IPv4_Pct: ipVersion.result?.summary_0?.IPv4,
        IPv6_Pct: ipVersion.result?.summary_0?.IPv6,
        HTTP_Pct: httpProtocol.result?.summary_0?.http,
        HTTPS_Pct: httpProtocol.result?.summary_0?.https,
        Desktop_Pct: deviceType.result?.summary_0?.desktop,
        Mobile_Pct: deviceType.result?.summary_0?.mobile,
        Bot_Pct: botType.result?.summary_0?.bot,
        Human_Pct: botType.result?.summary_0?.human
      };

      const formattedData = {};
      for (const [key, value] of Object.entries(cleanedData)) {
        if (value !== undefined && value !== null) {
          if (key === 'estimatedUsers') {
            formattedData[key] = parseFloat(value).toLocaleString();
          } else if (key.includes('_Pct') && !isNaN(parseFloat(value))) {
            formattedData[key] = `${parseFloat(value).toFixed(2)}%`;
          } else {
            formattedData[key] = value;
          }
        }
      }
      return Utils.jsonResponse(formattedData);
    } catch (error) {
      console.error('CloudflareRadar Error:', error.message);
      return Utils.errorResponse(error.message || 'Internal server error', 500);
    }
  }

  // 配置检查 - 修改为与Vercel版本兼容
  async handleConfigs(request) {
    const referer = request.headers.get('referer');
    if (!Utils.refererCheck(referer, ['ipcheck.ing', 'www.ipcheck.ing', 'localtest.ipcheck.ing'])) {
      return Utils.errorResponse(referer ? 'Access denied' : 'What are you doing?', 403);
    }

    const hostname = referer ? new URL(referer).hostname : '';
    // const allowedHostnames = ['ipcheck.ing', 'www.ipcheck.ing', 'localtest.ipcheck.ing', 'ip.wobshare.us.kg', 'wobshare.us.kg'];
    const allowedHostnames = ['*'];
    const originalSite = allowedHostnames.includes(hostname);


    // 返回与Vercel版本兼容的配置格式
    const result = {
      originalSite: originalSite ? hostname : '',
      googleMapAPIKey: this.env.GOOGLE_MAP_API_KEY || '',
      isFireBaseSet: false,
      allowedDomains: this.env.ALLOWED_DOMAINS || '',
      ipGeoSource: 0,
      ipCardsToShow: 6,
      status: 'active',
      version: '1.0.0',
      // 保留原有的布尔值配置用于兼容性
      map: !!this.env.GOOGLE_MAP_API_KEY,
      ipInfo: !!this.env.IPINFO_API_TOKEN,
      ipChecking: !!this.env.IPCHECKING_API_KEY,
      ip2location: !!this.env.IP2LOCATION_API_KEY,
      cloudFlare: !!this.env.CLOUDFLARE_API,
      ipapiis: !!this.env.IPAPIIS_API_KEY,
      maxmind: !!this.env.MAXMIND_LICENSE_KEY,
      macLookup: !!this.env.MAC_LOOKUP_API_KEY
    };

    return Utils.jsonResponse(result);
  }

  // IP 信息查询 - IPInfo.io (统一格式)
  async handleIPInfo(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));

    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      const token = Utils.getRandomKey(this.env.IPINFO_API_TOKEN);
      const apiUrl = token 
        ? `https://ipinfo.io/${ipAddress}?token=${token}`
        : `https://ipinfo.io/${ipAddress}/json`;

      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error(`IPinfo API Error: ${response.status}`);
      const data = await response.json();
      
      const [latitudeStr, longitudeStr] = (data.loc || 'N/A,N/A').split(','); // Changed '0,0' to 'N/A,N/A' for consistency
      const latitude = parseFloat(latitudeStr);
      const longitude = parseFloat(longitudeStr);
      
      const orgParts = (data.org || '').split(' ');
      const asn = orgParts.length > 1 && orgParts[0].toUpperCase().startsWith('AS') ? orgParts.shift() : 'N/A';
      const org = orgParts.join(' ') || 'N/A';
      
      const result = {
        ip: data.ip || 'N/A',
        city: data.city || 'N/A',
        region: data.region || 'N/A', // 对应前端 card.region (省份)
        country: data.country || 'N/A', // 国家代码
        country_name: data.country || 'N/A', // 国家名称 (IPinfo的country字段通常是代码，这里用作名称)
        country_code: data.country || 'N/A', // 国家代码
        latitude: !isNaN(latitude) ? latitude : 'N/A',
        longitude: !isNaN(longitude) ? longitude : 'N/A',
        asn: asn,
        isp: org, // 对应前端 card.isp (网络)
        postal: data.postal || 'N/A',
        timezone: data.timezone || 'N/A',
        raw_data: data // 保留原始数据
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IPInfo Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IP info', 500);
    }
  }

  // IP2Location API (统一格式)
  async handleIP2Location(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));

    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      const key = Utils.getRandomKey(this.env.IP2LOCATION_API_KEY);
      if (!key) return Utils.errorResponse('IP2Location API key not configured', 500);
      const apiUrl = `https://api.ip2location.io/?ip=${ipAddress}&key=${key}&format=json`;

      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error(`IP2Location API Error: ${response.status}`);
      const data = await response.json();

      if (data.error_message) {
        return Utils.errorResponse(`IP2Location: ${data.error_message}`, 400);
      }

      const result = {
        ip: data.ip || 'N/A',
        city: data.city_name || 'N/A',
        region: data.region_name || 'N/A', // 对应前端 card.region (省份)
        country: data.country_code || 'N/A', // 国家代码
        country_name: data.country_name || 'N/A', // 国家名称
        country_code: data.country_code || 'N/A', // 国家代码
        latitude: data.latitude !== undefined ? parseFloat(data.latitude) : 'N/A',
        longitude: data.longitude !== undefined ? parseFloat(data.longitude) : 'N/A',
        asn: data.asn ? 'AS' + data.asn : 'N/A',
        isp: data.as || 'N/A', // 对应前端 card.isp (网络)
        postal: data.zip_code || 'N/A', // IP2Location有zip_code
        timezone: data.timezone || 'N/A', // IP2Location有timezone
        raw_data: data // 保留原始数据
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IP2Location Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IP2Location data', 500);
    }
  }
  
  // MaxMind API (统一格式)
  async handleMaxMind(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));
    let lang = Utils.normalizeQueryParam(url.searchParams.get('lang')) || 'en';
  
    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
  
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }
  
    try {
      const accountId = this.env.MAXMIND_ACCOUNT_ID || '1174129';
      const licenseKey = this.env.MAXMIND_LICENSE_KEY;

      if (!licenseKey) {
        return Utils.errorResponse('MaxMind license key not configured.', 500);
      }
      if (!accountId) {
        return Utils.errorResponse('MaxMind account ID not configured.', 500);
      }

      const apiUrl = `https://geoip.maxmind.com/geoip/v2.1/city/${ipAddress}`;
  
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: 'Basic ' + btoa(`${accountId}:${licenseKey}`)
        }
      });
  
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`MaxMind API Error (${response.status}): ${errorText}`);
        return Utils.errorResponse(`MaxMind API Error: ${response.status} ${response.statusText}`, response.status);
      }
  
      const data = await response.json();
  
      const result = {
        ip: ipAddress || 'N/A',
        city: data.city?.names?.[lang] || data.city?.names?.en || 'N/A',
        region: data.subdivisions?.[0]?.names?.[lang] || data.subdivisions?.[0]?.names?.en || 'N/A', // 对应前端 card.region (省份)
        country: data.country?.iso_code || 'N/A', // 国家代码
        country_name: data.country?.names?.[lang] || data.country?.names?.en || 'N/A', // 国家名称
        country_code: data.country?.iso_code || 'N/A', // 国家代码
        latitude: data.location?.latitude !== undefined ? data.location.latitude : 'N/A',
        longitude: data.location?.longitude !== undefined ? data.location.longitude : 'N/A',
        asn: data.traits?.autonomous_system_number ? 'AS' + data.traits.autonomous_system_number : 'N/A',
        isp: data.traits?.autonomous_system_organization || 'N/A', // 对应前端 card.isp (网络)
        postal: data.postal?.code || 'N/A',
        timezone: data.location?.time_zone || 'N/A',
        raw_data: data // 保留原始数据
      };
  
      return Utils.jsonResponse(result);
  
    } catch (err) {
      console.error('MaxMind Error:', err.message);
      return Utils.errorResponse(err.message || 'Error fetching MaxMind data', 500);
    }
  }
  
  // IP-API.com (统一格式)
  async handleIPAPI(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));
    let lang = Utils.normalizeQueryParam(url.searchParams.get('lang')) || 'en';
    
    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      const fields = "status,message,continent,continentCode,country,countryCode,region,regionName,city,district,zip,lat,lon,timezone,isp,org,as,asname,query";
      const apiUrl = `http://ip-api.com/json/${ipAddress}?fields=${fields}&lang=${lang}`;
      
      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error(`IP-API.com API Error: ${response.status}`);
      const data = await response.json();

      if (data.status === 'fail') {
        return Utils.errorResponse(`IP-API.com: ${data.message || 'Failed to retrieve data'}`, 400);
      }

      const { query, country, countryCode, regionName, city, lat, lon, isp, org, as: asField, asname } = data;
      const asnNumber = asField ? asField.split(" ")[0] : 'N/A';
      const asOrg = asname || (asField ? asField.substring(asField.indexOf(" ") + 1) : 'N/A');

      const result = {
        ip: query || 'N/A',
        city: city || 'N/A',
        region: regionName || 'N/A', // 对应前端 card.region (省份)
        country: countryCode || 'N/A', // 国家代码
        country_name: country || 'N/A', // 国家名称
        country_code: countryCode || 'N/A', // 国家代码
        latitude: lat !== undefined ? lat : 'N/A',
        longitude: lon !== undefined ? lon : 'N/A',
        asn: asnNumber,
        isp: isp || org || asOrg || 'N/A', // 对应前端 card.isp (网络), 优先isp，其次org，最后asOrg
        postal: data.zip || 'N/A',
        timezone: data.timezone || 'N/A',
        raw_data: data // 保留原始数据
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IP-API.com Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IP-API.com data', 500);
    }
  }

  // IPAPI.is (统一格式)
  async handleIPAPIIS(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));
    
    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      const key = Utils.getRandomKey(this.env.IPAPIIS_API_KEY);
      if (!key) return Utils.errorResponse('IPAPI.is API key not configured', 500);
      
      const apiUrl = `https://api.ipapi.is?q=${ipAddress}&key=${key}`;

      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error(`IPAPI.is API Error: ${response.status}`);
      const data = await response.json();

      if (data.error) {
        return Utils.errorResponse(`IPAPI.is: ${data.error.message || data.error}`, data.error.code || 400);
      }

      const { ip, location, company, asn, abuse } = data;

      const result = {
        ip: ip || 'N/A',
        city: location?.city || 'N/A',
        region: location?.state || 'N/A', // 对应前端 card.region (省份)
        country: location?.country_code || 'N/A', // 国家代码
        country_name: location?.country || 'N/A', // 国家名称
        country_code: location?.country_code || 'N/A', // 国家代码
        latitude: location?.latitude !== undefined ? location.latitude : 'N/A',
        longitude: location?.longitude !== undefined ? location.longitude : 'N/A',
        asn: asn?.asn ? 'AS' + asn.asn : 'N/A',
        isp: company?.name || asn?.org || 'N/A', // 对应前端 card.isp (网络), 优先公司名，其次asn组织名
        postal: location?.zip || 'N/A', // IPAPI.is 有 zip
        timezone: location?.timezone || 'N/A', // IPAPI.is 有 timezone
        raw_data: data // 保留原始数据
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IPAPI.is Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IPAPI.is data', 500);
    }
  }

  // IP.sb (统一格式)
  async handleIPSB(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));
    
    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      const apiUrl = `https://api.ip.sb/geoip/${ipAddress}`;
      const response = await fetch(apiUrl, { headers: { 'Accept': 'application/json' } });
      if (!response.ok) throw new Error(`IP.sb API Error: ${response.status}`);
      const data = await response.json();

      const result = {
        ip: data.ip || 'N/A',
        city: data.city || 'N/A',
        region: data.region || 'N/A', // 对应前端 card.region (省份)
        country: data.country_code || 'N/A', // 国家代码
        country_name: data.country || 'N/A', // 国家名称
        country_code: data.country_code || 'N/A', // 国家代码
        latitude: data.latitude !== undefined && data.latitude !== '' ? data.latitude : 'N/A',
        longitude: data.longitude !== undefined && data.longitude !== '' ? data.longitude : 'N/A',
        asn: data.asn ? "AS" + data.asn : 'N/A',
        isp: data.isp || 'N/A', // 对应前端 card.isp (网络)
        postal: data.zip || 'N/A', // IP.sb有zip
        timezone: data.timezone || 'N/A', // IP.sb有timezone
        raw_data: data // 保留原始数据
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IP.sb Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IP.sb data', 500);
    }
  }

  // IPAPI.co API (统一格式)
  async handleIPAPICo(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));

    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }

    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      // 直接返回IP信息，不调用外部API（避免配额问题）
      const result = {
        ip: ipAddress,
        city: 'Unknown',
        region: 'Unknown',
        country: 'Unknown',
        country_name: 'Unknown',
        country_code: 'Unknown',
        latitude: 'N/A',
        longitude: 'N/A',
        asn: 'N/A',
        isp: 'Unknown',
        postal: 'N/A',
        timezone: 'N/A',
        source: 'IPAPI.co API'
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('IPAPI.co Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching IPAPI.co data', 500);
    }
  }

  // MAC 地址查询
  async handleMACChecker(request) {
    const url = new URL(request.url);
    let macAddressParam = Utils.normalizeQueryParam(url.searchParams.get('mac'));
    
    if (!macAddressParam) {
      return Utils.errorResponse('No MAC address provided');
    }

    const normalizedMac = macAddressParam.replace(/[:-]/g, '').toUpperCase();
    
    if (!Utils.isValidMAC(normalizedMac)) {
      return Utils.errorResponse('Invalid MAC address format');
    }
    
    const oui = normalizedMac.substring(0, 6);

    try {
      const token = this.env.MAC_LOOKUP_API_KEY;
      const apiUrl = token 
        ? `https://api.maclookup.app/v2/macs/${oui}?apiKey=${token}`
        : `https://api.maclookup.app/v2/macs/${oui}`;

      const response = await fetch(apiUrl);
      if (!response.ok) {
         const errorData = await response.json().catch(() => ({ error: `MAC Lookup API Error: ${response.status}` }));
         return Utils.errorResponse(errorData.error || `MAC Lookup API Error: ${response.status}`, response.status);
      }
      const data = await response.json();

      if (data.success !== true) {
        return Utils.jsonResponse({ success: false, error: data.error || data.message || 'Data not found' });
      }

      const firstByte = parseInt(oui.substring(0, 2), 16);
      const isMulticast = (firstByte & 0x01) === 0x01;
      const isLocal = (firstByte & 0x02) === 0x02;

      const result = {
        success: true,
        found: data.found,
        macPrefix: data.macPrefix ? data.macPrefix.match(/.{1,2}/g).join(':') : oui.match(/.{1,2}/g).join(':'),
        company: data.company || 'N/A',
        country: data.country || 'N/A',
        address: data.address || 'N/A',
        blockStart: data.blockStart ? data.blockStart.match(/.{1,2}/g).join(':') : 'N/A',
        blockEnd: data.blockEnd ? data.blockEnd.match(/.{1,2}/g).join(':') : 'N/A',
        blockSize: data.blockSize || 'N/A',
        blockType: data.blockType || 'N/A',
        isMulticast: isMulticast,
        isUnicast: !isMulticast,
        isLocalAdministered: isLocal,
        isUniversallyAdministered: !isLocal,
        lastUpdate: data.updated || data.lastUpdate || 'N/A'
      };

      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('MAC Checker Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching MAC data', 500);
    }
  }

  // WHOIS 查询
  async handleWhois(request) {
    const url = new URL(request.url);
    const query = Utils.normalizeQueryParam(url.searchParams.get('q'));
    
    if (!query) {
      return Utils.errorResponse('No address (IP or domain) provided for WHOIS lookup');
    }
    
    if (!Utils.isValidIP(query) && !Utils.isValidDomain(query)) {
      return Utils.errorResponse('Invalid input for WHOIS: Must be a valid IP or domain name');
    }
    
    // *** MODIFICATION: Added a placeholder for a real WHOIS API call ***
    // For a real implementation, you'd call an external WHOIS API here.
    // Example: using ip2whois.com (requires API key)
    // const apiKey = this.env.IP2WHOIS_API_KEY;
    // if (!apiKey) return Utils.errorResponse('WHOIS API key not configured', 500);
    // const whoisApiUrl = `https://api.ip2whois.com/v2?key=${apiKey}&domain=${encodeURIComponent(query)}`;
    // try {
    //   const response = await fetch(whoisApiUrl);
    //   if (!response.ok) {
    //      const errData = await response.json().catch(()=>null);
    //      throw new Error(`WHOIS API Error: ${response.status} - ${errData ? errData.error_message : response.statusText}`);
    //   }
    //   const data = await response.json();
    //   return Utils.jsonResponse(data);
    // } catch (error) {
    //   console.error('WHOIS Error:', error.message);
    //   return Utils.errorResponse(error.message || 'Error fetching WHOIS data', 500);
    // }
    return Utils.errorResponse('WHOIS service not directly implemented. An external API call is required.', 501);
  }

  // Google Maps 静态地图
  async handleGoogleMap(request) {
    const url = new URL(request.url);
    const latitude = Utils.normalizeQueryParam(url.searchParams.get('latitude'));
    const longitude = Utils.normalizeQueryParam(url.searchParams.get('longitude'));
    const language = Utils.normalizeQueryParam(url.searchParams.get('language'));
    const canvasMode = Utils.normalizeQueryParam(url.searchParams.get('CanvasMode'));
    
    if (!latitude || !longitude || !language) {
      return Utils.errorResponse('Missing latitude, longitude, or language parameters');
    }

    const isLatitudeValid = /^-?\d+(\.\d+)?$/.test(latitude);
    const isLongitudeValid = /^-?\d+(\.\d+)?$/.test(longitude);
    const isLanguageValid = /^[a-zA-Z]{2}(-[a-zA-Z]{2})?$/.test(language);

    if (!isLatitudeValid || !isLongitudeValid || !isLanguageValid) {
      return Utils.errorResponse('Invalid request parameters for Google Map');
    }

    try {
      const apiKey = Utils.getRandomKey(this.env.GOOGLE_MAP_API_KEY);
      if (!apiKey) return Utils.errorResponse('Google Maps API key not configured', 500);

      const mapSize = '500x400';
      const scale = 2;
      const zoom = 3;

      let styleParams = [];
      if (canvasMode === 'Dark') {
        styleParams.push("style=element:geometry|color:0x242f3e");
        styleParams.push("style=element:labels.text.fill|color:0x746855");
        styleParams.push("style=element:labels.text.stroke|color:0x242f3e");
        styleParams.push("style=feature:administrative.locality|element:labels.text.fill|color:0xd59563");
        styleParams.push("style=feature:poi|element:labels.text.fill|color:0xd59563");
        styleParams.push("style=feature:poi.park|element:geometry|color:0x263c3f");
        styleParams.push("style=feature:poi.park|element:labels.text.fill|color:0x6b9a76");
        styleParams.push("style=feature:road|element:geometry|color:0x38414e");
        styleParams.push("style=feature:road|element:geometry.stroke|color:0x212a37");
        styleParams.push("style=feature:road|element:labels.text.fill|color:0x9ca5b3");
        styleParams.push("style=feature:road.highway|element:geometry|color:0x746855");
        styleParams.push("style=feature:road.highway|element:geometry.stroke|color:0x1f2835");
        styleParams.push("style=feature:road.highway|element:labels.text.fill|color:0xf3d19c");
        styleParams.push("style=feature:transit|element:geometry|color:0x2f3948");
        styleParams.push("style=feature:transit.station|element:labels.text.fill|color:0xd59563");
        styleParams.push("style=feature:water|element:geometry|color:0x17263c");
        styleParams.push("style=feature:water|element:labels.text.fill|color:0x515c6d");
        styleParams.push("style=feature:water|element:labels.text.stroke|color:0x17263c");
      }
      const styleQuery = styleParams.length > 0 ? `&${styleParams.join('&')}` : '';

      const mapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&markers=color:blue%7C${latitude},${longitude}&scale=${scale}&zoom=${zoom}&maptype=roadmap&language=${language}&format=jpg&size=${mapSize}${styleQuery}&key=${apiKey}`;

      const response = await fetch(mapUrl);
      if (!response.ok) {
        console.error(`Google Maps API Error: ${response.status}`);
        return Utils.errorResponse(`Error fetching map from Google: ${response.status}`, response.status);
      }
      return new Response(response.body, {
        status: response.status,
        headers: { 
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Access-Control-Allow-Origin': '*' 
        }
      });
    } catch (error) {
      console.error('GoogleMap Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching map', 500);
    }
  }

  // *** 新增/修改 ***
  // DNS 解析器
  async handleDNSResolver(request) {
    const url = new URL(request.url);
    const hostname = Utils.normalizeQueryParam(url.searchParams.get('hostname'));
    let type = Utils.normalizeQueryParam(url.searchParams.get('type')) || 'A'; // 默认为 A 记录
    
    if (!hostname) {
      return Utils.errorResponse('Missing hostname parameter for DNS resolution');
    }
    
    if (!Utils.isValidDomain(hostname)) {
      return Utils.errorResponse(`Invalid hostname: ${hostname}`);
    }

    // 常见的 DNS 记录类型，根据需要可以扩展
    const validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SRV', 'CAA', 'PTR']; 
    type = type.toUpperCase(); // 将类型转换为大写以便比较
    if (!validTypes.includes(type)) {
        return Utils.errorResponse(`Invalid DNS record type: ${type}. Valid types: ${validTypes.join(', ')}`);
    }

    try {
      // 定义多个 DoH 服务提供商
      // 注意：某些 DoH 服务可能对请求频率有限制
      const dohServers = {
        'Google': `https://dns.google/resolve?name=${encodeURIComponent(hostname)}&type=${type}`,
        'Cloudflare': `https://cloudflare-dns.com/dns-query?name=${encodeURIComponent(hostname)}&type=${type}`,
        'AdGuard': `https://dns.adguard-dns.com/resolve?name=${encodeURIComponent(hostname)}&type=${type}`,
        // 'Quad9': `https://dns.quad9.net:5053/dns-query?name=${encodeURIComponent(hostname)}&type=${type}`, // 需要检查是否支持GET或特定参数
      };

      const fetchPromises = Object.entries(dohServers).map(async ([name, serverUrl]) => {
        try {
          const response = await fetch(serverUrl, {
            headers: { 'Accept': 'application/dns-json' } // 标准的 DoH JSON 响应头
          });

          if (!response.ok) {
            console.warn(`DoH server ${name} for ${hostname} ${type} returned ${response.status}`);
            return { name, statusText: response.statusText, status: response.status, error: `Failed with status ${response.status}`, addresses: ['N/A'] };
          }
          
          const data = await response.json();
          
          // 根据 RFC 8484, Status 0 表示 NOERROR
          if (data.Status === 0 && data.Answer) {
            const addresses = data.Answer.map(answer => {
                // data.Answer[i].type 对应 DNS 记录类型数字
                // 1: A, 2: NS, 5: CNAME, 6: SOA, 12: PTR, 15: MX, 16: TXT, 28: AAAA, 257: CAA
                switch (answer.type) {
                    case 1: // A
                    case 28: // AAAA
                    case 5: // CNAME
                    case 2: // NS
                    case 12: // PTR
                        return answer.data;
                    case 16: // TXT - TXT 记录可能是分段的，DoH 通常会合并
                        // Google DoH 返回的是一个字符串数组，Cloudflare 是单个拼接好的字符串
                        return Array.isArray(answer.data) ? answer.data.join('') : answer.data.replace(/" "/g, '').replace(/^"|"$/g, '');
                    case 15: // MX - 格式: "priority host."
                        // data.data 格式: "10 mail.example.com."
                        const parts = answer.data.split(" ");
                        return `${parts[0]} ${parts[1]}`; 
                    case 257: // CAA - 格式: "flags tag value"
                        // data.data 格式: "0 issue \"letsencrypt.org\"" (通常 flags 是数字)
                        return answer.data;
                    default:
                        return `Type ${answer.type}: ${answer.data}`; // 其他类型直接返回数据
                }
            });
            return { name, status: data.Status, addresses: addresses.length > 0 ? addresses : ['No records found'] };
          } else if (data.Status === 3) { // NXDOMAIN
            return { name, status: data.Status, error: 'NXDOMAIN (Non-Existent Domain)', addresses: [] };
          } else {
            // 其他错误状态，例如 SERVFAIL (Status 2), FORMERR (Status 1)
            const errorMsg = data.Comment || (data.Status !== undefined ? `DNS Error Status ${data.Status}` : 'No answer or unknown error');
            return { name, status: data.Status !== undefined ? data.Status : 'Error', error: errorMsg, addresses: ['N/A'] };
          }
        } catch (e) {
          console.warn(`Error fetching from DoH server ${name} for ${hostname} ${type}: ${e.message}`);
          return { name, status: 'FetchError', error: e.message, addresses: ['N/A'] };
        }
      });

      const resultsArray = await Promise.all(fetchPromises);
      
      // 将结果数组转换为以服务器名称为键的对象
      const resultsObject = resultsArray.reduce((acc, curr) => {
        acc[curr.name] = { status: curr.status, addresses: curr.addresses };
        if(curr.statusText) acc[curr.name].statusText = curr.statusText;
        if(curr.error) acc[curr.name].error = curr.error;
        return acc;
      }, {});

      return Utils.jsonResponse({
        query: { hostname, type },
        results: resultsObject
      });

    } catch (error) {
      console.error('DNSResolver Error:', error.message, error.stack);
      return Utils.errorResponse(error.message || 'Error resolving DNS', 500);
    }
  }

  // IPChecking API 相关处理
  async handleIPChecking(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));
    let lang = Utils.normalizeQueryParam(url.searchParams.get('lang')) || 'en';
    
    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    const key = this.env.IPCHECKING_API_KEY;
    const apiEndpoint = this.env.IPCHECKING_API_ENDPOINT;

    if (!key || !apiEndpoint) {
      return Utils.errorResponse('IPChecking API key or endpoint is missing in configuration', 500);
    }

    try {
      const apiUrl = `${apiEndpoint}/ipinfo?key=${key}&ip=${ipAddress}&lang=${lang}`;
      const headersToSend = new Headers(request.headers);
      headersToSend.delete('cf-connecting-ip');
      headersToSend.delete('cf-ipcountry');
      headersToSend.delete('cf-ray');
      headersToSend.delete('cf-visitor');
      headersToSend.delete('x-forwarded-proto');
      headersToSend.delete('x-real-ip');

      const response = await fetch(apiUrl, {
        method: request.method,
        headers: headersToSend,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`IPChecking API responded with status: ${response.status}. Body: ${errorText}`);
      }

      const data = await response.json();
      return Utils.jsonResponse(data);
    } catch (error) {
      console.error('IPChecking API Error:', error.message);
      return Utils.errorResponse(error.message, 500);
    }
  }

  // 用户信息获取
  async handleGetUserInfo(request) {
    const key = this.env.IPCHECKING_API_KEY;
    const apiEndpoint = this.env.IPCHECKING_API_ENDPOINT;

    if (!key || !apiEndpoint) {
      return Utils.errorResponse('IPChecking API key or endpoint is missing for GetUserInfo', 500);
    }

    try {
      const apiUrl = `${apiEndpoint}/userinfo?key=${key}`;
      const headersToSend = new Headers(request.headers);
      // Clean headers as needed

      const response = await fetch(apiUrl, {
        headers: headersToSend
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`GetUserInfo API responded with status: ${response.status}. Body: ${errorText}`);
      }

      const data = await response.json();
      return Utils.jsonResponse(data);
    } catch (error) {
      console.error('GetUserInfo API Error:', error.message);
      return Utils.errorResponse(error.message, 500);
    }
  }

  // 隐身测试
  async handleInvisibilityTest(request) {
    const url = new URL(request.url);
    const id = Utils.normalizeQueryParam(url.searchParams.get('id'));
    
    if (!id) {
      return Utils.errorResponse('No ID provided for Invisibility Test');
    }
    
    if (!Utils.isValidUserID(id)) {
      return Utils.errorResponse('Invalid ID format for Invisibility Test');
    }

    const apikey = this.env.IPCHECKING_API_KEY;
    const apiEndpoint = this.env.IPCHECKING_API_ENDPOINT;

    if (!apikey || !apiEndpoint) {
      return Utils.errorResponse('IPChecking API key or endpoint is missing for Invisibility Test', 500);
    }

    try {
      const apiUrl = `${apiEndpoint}/getpdresult/${id}?apikey=${apikey}`;
      const headersToSend = new Headers(request.headers);
      // Clean headers if necessary

      const response = await fetch(apiUrl, {
        headers: headersToSend
      });

      if (!response.ok) {
        let errorDetail = response.statusText;
        try {
          const errorData = await response.json();
          errorDetail = errorData.message || JSON.stringify(errorData);
        } catch (e) { /* ignore if body is not json */ }
        throw new Error(`Invisibility Test API responded with status: ${response.status} - ${errorDetail}`);
      }

      const data = await response.json();
      return Utils.jsonResponse(data);
    } catch (error) {
      console.error('InvisibilityTest API Error:', error.message);
      return Utils.errorResponse(error.message, 500);
    }
  }

  // 更新用户成就
  async handleUpdateUserAchievement(request) {
    if (request.method !== 'PUT' && request.method !== 'POST') {
      return Utils.errorResponse('Method not allowed (expected PUT or POST)', 405);
    }

    const key = this.env.IPCHECKING_API_KEY;
    const apiEndpoint = this.env.IPCHECKING_API_ENDPOINT;

    if (!key || !apiEndpoint) {
      return Utils.errorResponse('IPChecking API key or endpoint is missing for UpdateUserAchievement', 500);
    }

    try {
      const body = await request.json().catch(() => null);
      if (!body) {
        return Utils.errorResponse('Achievement data (JSON body) is required');
      }

      const apiUrl = `${apiEndpoint}/updateuserachievements?key=${key}`;
      
      const headersToSend = new Headers(request.headers);
      headersToSend.set('Content-Type', 'application/json');
      // Clean other headers if necessary

      const response = await fetch(apiUrl, {
        method: request.method,
        headers: headersToSend,
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`UpdateUserAchievement API responded with status: ${response.status}. Body: ${errorText}`);
      }

      const data = await response.json();
      return Utils.jsonResponse(data);
    } catch (error) {
      console.error('UpdateUserAchievement API Error:', error.message);
      return Utils.errorResponse(error.message, 500);
    }
  }

  // *** 增加美团 API 代理方法 ***
  async handleMeituanIP(request) {
    const url = new URL(request.url);
    let ipAddress = Utils.normalizeQueryParam(url.searchParams.get('ip'));

    if (!ipAddress) {
      ipAddress = this.getClientIP(request);
      if (!ipAddress) {
        return Utils.errorResponse('No IP address provided and unable to determine client IP');
      }
    }
    
    if (!Utils.isValidIP(ipAddress)) {
      return Utils.errorResponse(`Invalid IP address: '${ipAddress}'`);
    }

    try {
      // 注意：client_source=yourAppKey 需要替换为实际的AppKey
      const apiUrl = `https://apimobile.meituan.com/locate/v2/ip/loc?client_source=yourAppKey&rgeo=true&ip=${ipAddress}`;
      
      const response = await fetch(apiUrl);
      if (!response.ok) {
        let errorMsg = `Meituan API Error: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMsg += ` - ${errorData.message || JSON.stringify(errorData)}`;
        } catch (e) {
          errorMsg += ` - ${await response.text()}`;
        }
        throw new Error(errorMsg);
      }
      
      const data = await response.json();
      
      // 严格按照你提供的美团API返回结构进行映射，并填充N/A
      // 原始 raw_data":{"data":{"lng":114.31,"fromwhere":"mars-mt","ip":"**************","rgeo":{"country":"中国","province":"广东省","adcode":"441322","city":"惠州","district":"博罗县"},"lat":23.17}}}
      const result = {
        ip: ipAddress || 'N/A', 
        country: data.data?.rgeo?.country || 'N/A',       // 国家 (例如 "中国")
        country_name: data.data?.rgeo?.country || 'N/A',  // 国家名称 (美团直接返回名称)
        province: data.data?.rgeo?.province || 'N/A',     // 省份 (例如 "广东省")
        city: data.data?.rgeo?.city || 'N/A',             // 城市 (例如 "惠州")
        isp: data.data?.rgeo?.district || 'N/A', // 根据你的要求，用 district 填充 isp (对应前端 network)
        asn: 'N/A',     // 美团API不直接提供ASN信息，统一返回N/A
        latitude: data.data?.lat || 'N/A',                // 纬度
        longitude: data.data?.lng || 'N/A',               // 经度
        postal: data.data?.rgeo?.adcode || 'N/A',         // adcode 可以作为近似的邮政编码或行政区划代码
        timezone: 'N/A', // 美团API不直接提供时区
        raw_data: data // 包含原始数据
      };
      return Utils.jsonResponse(result);
    } catch (error) {
      console.error('Meituan IP API Error:', error.message);
      return Utils.errorResponse(error.message || 'Error fetching Meituan IP data', 500);
    }
  }
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    let pathname = url.pathname;
    if (pathname.length > 1 && pathname.endsWith('/')) {
      pathname = pathname.slice(0, -1);
    }

    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, OPTIONS, DELETE, PATCH",
      "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Cf-Connecting-IP",
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }
    
    // 从环境变量中解析允许的 Referer 域名 (注意：这里使用 env.ALLOWED_REFERER_DOMAINS 变量)
    const allowedRefererDomainsFromEnv = (env.ALLOWED_REFERER_DOMAINS || "yourdomain.com,anotherdomain.net").split(',').map(d => d.trim());

    // *** 日志调试信息 ***
    console.log(`--- Worker Request Debug Start ---`);
    console.log(`Request URL: ${request.url}`);
    console.log(`Pathname: ${pathname}`);
    console.log(`Referer Header received: ${request.headers.get('referer')}`);
    console.log(`env.ALLOWED_REFERER_DOMAINS (raw from env): "${env.ALLOWED_REFERER_DOMAINS}"`); // 确保环境变量名正确
    console.log(`allowedRefererDomainsFromEnv (parsed list for Utils.refererCheck): ${JSON.stringify(allowedRefererDomainsFromEnv)}`);
    console.log(`--- Worker Request Debug End ---`);
    // --- 日志调试信息结束 ---


    // 所有IP查询的API路径都纳入 protectedEndpoints 数组中
    const protectedEndpoints = [
      '/api/cf-radar',
      '/api/configs',
      '/api/google-map',
      '/api/dns-resolver',
      '/api/get-user-info',
      '/api/invisibility-test',
      '/api/update-user-achievement',
      // 所有IP查询接口
      '/api/ipapicom',
      '/api/ipsb',
      '/api/ipinfo',
      '/api/ipapiis',
      '/api/ip2location',
      '/api/ipapi', // 这个可能和 ipapicom 重复，请检查是否需要
      '/api/maxmind',
      '/api/meituan-ip',
      '/api/ipapico'
    ];

    if (protectedEndpoints.some(endpoint => pathname === endpoint || pathname.startsWith(endpoint + '/'))) {
      const referer = request.headers.get('referer');
      // 保持你原始代码中的 Referer 检查逻辑，并传入从环境变量解析出的域名
      if (!Utils.refererCheck(referer, allowedRefererDomainsFromEnv)) { // <--- 这里是修复点
         console.warn(`Referer check failed for ${pathname}. Referer: ${referer}`);
         const errorResp = Utils.errorResponse(referer ? 'Access denied: Invalid referer' : 'Access denied: Referer missing', 403);
         Object.entries(corsHeaders).forEach(([key, value]) => errorResp.headers.set(key, value));
         return errorResp;
      }
    }

    const handlers = new APIHandlers(env);
    let response;

    try {
      switch (pathname) {
        case '/api/maxmind':
          response = await handlers.handleMaxMind(request);
          break;
        case '/api/ipapicom':
          response = await handlers.handleIPAPI(request);
          break;
        case '/api/cf-radar':
          response = await handlers.handleCloudflareRadar(request);
          break;
        case '/api/configs':
          response = await handlers.handleConfigs(request);
          break;
        case '/api/ipinfo':
          response = await handlers.handleIPInfo(request);
          break;
        case '/api/ip2location':
          response = await handlers.handleIP2Location(request);
          break;
        case '/api/ipapi':
          response = await handlers.handleIPAPI(request);
          break;
        case '/api/ipapiis':
          response = await handlers.handleIPAPIIS(request);
          break;
        case '/api/ipsb':
          response = await handlers.handleIPSB(request);
          break;
        case '/api/mac-checker':
          response = await handlers.handleMACChecker(request);
          break;
        case '/api/whois':
          response = await handlers.handleWhois(request);
          break;
        case '/api/google-map':
          response = await handlers.handleGoogleMap(request);
          break;
        // *** 新增/修改 ***: 添加 dns-resolver 路由
        case '/api/dns-resolver':
          response = await handlers.handleDNSResolver(request);
          break;
        case '/api/ipchecking':
          response = await handlers.handleIPChecking(request);
          break;
        case '/api/get-user-info':
          response = await handlers.handleGetUserInfo(request);
          break;
        case '/api/invisibility-test':
          response = await handlers.handleInvisibilityTest(request);
          break;
        case '/api/update-user-achievement':
          response = await handlers.handleUpdateUserAchievement(request);
          break;
        // *** 增加美团API路由 ***
        case '/api/meituan-ip':
          response = await handlers.handleMeituanIP(request);
          break;
        // *** 增加IPAPI.co路由 ***
        case '/api/ipapico':
          response = await handlers.handleIPAPICo(request);
          break;
        default:
          response = Utils.errorResponse(`Not Found: The path '${pathname}' was not found. Original path: '${url.pathname}'`, 404);
      }
    } catch (error) {
      console.error(`Unhandled Handler error for ${pathname}:`, error.stack || error);
      response = Utils.errorResponse('Internal Server Error', 500);
    }

    if (response instanceof Response) {
        const newHeaders = new Headers(response.headers);
        Object.entries(corsHeaders).forEach(([key, value]) => {
            if (!newHeaders.has(key)) {
                newHeaders.set(key, value);
            }
        });
        newHeaders.set("Access-Control-Allow-Origin", corsHeaders["Access-Control-Allow-Origin"]);

        return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: newHeaders
        });
    }
    return response; 
  }
};