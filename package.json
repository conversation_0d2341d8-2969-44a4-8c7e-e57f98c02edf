{"name": "myip", "private": true, "version": "5.0.0", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"nodemon backend-server.js\"", "build": "vite build", "vercel-build": "vite build", "preview": "vite preview", "start-backend": "node backend-server.js", "start-frontend": "node frontend-server.js", "start": "concurrently \"npm run start-frontend\" \"npm run start-backend\""}, "dependencies": {"@analytics/google-analytics": "^1.1.0", "@cloudflare/speedtest": "^1.4.1", "@khmyznikov/pwa-install": "^0.5.4", "@thumbmarkjs/thumbmarkjs": "^0.20.0", "@vue-leaflet/vue-leaflet": "^0.10.1", "analytics": "^0.8.16", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.8", "circle-progress.vue": "^3.3.0", "code-inspector-plugin": "^0.20.7", "concurrently": "^9.1.2", "country-code-lookup": "^0.1.3", "detect-gpu": "^5.0.70", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.0.3", "firebase": "^11.5.0", "flag-icons": "^7.3.2", "http-proxy-middleware": "^3.0.3", "leaflet": "^1.9.4", "maxmind": "^4.3.24", "nodemon": "^3.1.9", "pinia": "^3.0.1", "svgmap": "^2.12.2", "ua-parser-js": "^2.0.3", "vue": "^3.5.13", "vue-i18n": "^11.1.2", "vue-markdown-render": "^2.2.1", "vue-router": "^4.5.0", "whoiser": "^1.18.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.27.0", "vite": "^6.2.3", "vite-plugin-pwa": "^1.0.0"}}