// IP质量分计算工具
// 基于多个因素计算真实的IP质量分数

/**
 * 计算IP质量分数
 * @param {Object} ipData - IP信息数据
 * @param {string} ip - IP地址
 * @returns {Object} - 包含分数和详细说明的对象
 */
export function calculateIPQualityScore(ipData, ip) {
  let score = 100; // 基础分数
  let factors = []; // 评分因素说明
  
  // 1. IP类型评估 (权重: 30%)
  const ipTypeScore = evaluateIPType(ipData, ip);
  score -= ipTypeScore.penalty;
  factors.push(...ipTypeScore.factors);
  
  // 2. 地理位置一致性评估 (权重: 20%)
  const geoScore = evaluateGeolocation(ipData);
  score -= geoScore.penalty;
  factors.push(...geoScore.factors);
  
  // 3. ISP信誉评估 (权重: 25%)
  const ispScore = evaluateISP(ipData);
  score -= ispScore.penalty;
  factors.push(...ispScore.factors);
  
  // 4. 网络基础设施评估 (权重: 15%)
  const infraScore = evaluateInfrastructure(ipData);
  score -= infraScore.penalty;
  factors.push(...infraScore.factors);
  
  // 5. 安全风险评估 (权重: 10%)
  const securityScore = evaluateSecurity(ip);
  score -= securityScore.penalty;
  factors.push(...securityScore.factors);
  
  // 确保分数在0-100范围内
  score = Math.max(0, Math.min(100, Math.round(score)));
  
  return {
    score,
    factors,
    explanation: generateExplanation(score, factors)
  };
}

/**
 * 评估IP类型
 */
function evaluateIPType(ipData, ip) {
  let penalty = 0;
  let factors = [];
  
  // IPv6 加分
  if (ip.includes(':')) {
    penalty -= 5; // IPv6 质量通常更好
    factors.push('IPv6地址，质量更好 (+5分)');
  }

  // 检查是否为代理/VPN
  if (ipData.isProxy && ipData.isProxy !== '否' && ipData.isProxy !== 'No') {
    penalty += 25;
    factors.push('使用了代理或VPN (-25分)');
  }

  // 检查IP类型
  if (ipData.type) {
    const type = ipData.type.toLowerCase();
    if (type.includes('datacenter') || type.includes('hosting')) {
      penalty += 15;
      factors.push('服务器IP，容易被拦截 (-15分)');
    } else if (type.includes('residential')) {
      penalty -= 5;
      factors.push('家庭宽带IP，质量好 (+5分)');
    } else if (type.includes('business')) {
      penalty -= 2;
      factors.push('企业IP，质量较好 (+2分)');
    }
  }
  
  return { penalty, factors };
}

/**
 * 评估地理位置一致性
 */
function evaluateGeolocation(ipData) {
  let penalty = 0;
  let factors = [];
  
  // 检查地理信息完整性
  if (!ipData.country_name || !ipData.city) {
    penalty += 10;
    factors.push('位置信息不全 (-10分)');
  } else {
    factors.push('位置信息完整 (+0分)');
  }

  // 检查坐标有效性
  if (ipData.latitude && ipData.longitude) {
    const lat = parseFloat(ipData.latitude);
    const lon = parseFloat(ipData.longitude);
    if (lat === 0 && lon === 0) {
      penalty += 8;
      factors.push('位置坐标异常 (-8分)');
    } else {
      factors.push('位置坐标正常 (+0分)');
    }
  }
  
  return { penalty, factors };
}

/**
 * 评估ISP信誉
 */
function evaluateISP(ipData) {
  let penalty = 0;
  let factors = [];
  
  if (!ipData.isp) {
    penalty += 5;
    factors.push('网络运营商信息缺失 (-5分)');
    return { penalty, factors };
  }

  const isp = ipData.isp.toLowerCase();

  // 知名ISP加分
  const reputableISPs = [
    'google', 'amazon', 'microsoft', 'cloudflare', 'china telecom',
    'china unicom', 'china mobile', 'comcast', 'verizon', 'att'
  ];

  const isReputable = reputableISPs.some(provider => isp.includes(provider));
  if (isReputable) {
    penalty -= 8;
    factors.push('知名网络运营商 (+8分)');
  }

  // 可疑ISP扣分
  const suspiciousKeywords = [
    'vpn', 'proxy', 'anonymous', 'tor', 'hosting', 'server', 'cloud'
  ];

  const isSuspicious = suspiciousKeywords.some(keyword => isp.includes(keyword));
  if (isSuspicious) {
    penalty += 20;
    factors.push('可疑网络服务商 (-20分)');
  }

  if (!isReputable && !isSuspicious) {
    factors.push('普通网络运营商 (+0分)');
  }
  
  return { penalty, factors };
}

/**
 * 评估网络基础设施
 */
function evaluateInfrastructure(ipData) {
  let penalty = 0;
  let factors = [];
  
  // ASN评估
  if (ipData.asn) {
    const asnNumber = parseInt(ipData.asn.replace(/[^\d]/g, ''));
    
    // 大型ASN通常更稳定
    if (asnNumber < 1000) {
      penalty -= 5;
      factors.push('老牌网络编号，更稳定 (+5分)');
    } else if (asnNumber > 60000) {
      penalty += 3;
      factors.push('较新网络编号 (-3分)');
    } else {
      factors.push('标准网络编号 (+0分)');
    }
  } else {
    penalty += 8;
    factors.push('网络编号信息缺失 (-8分)');
  }
  
  return { penalty, factors };
}

/**
 * 评估安全风险
 */
function evaluateSecurity(ip) {
  let penalty = 0;
  let factors = [];
  
  // 基于IP模式的简单风险评估
  
  // 检查是否为私有IP
  if (isPrivateIP(ip)) {
    penalty += 15;
    factors.push('内网IP地址 (-15分)');
  }

  // 检查是否为保留IP段
  if (isReservedIP(ip)) {
    penalty += 10;
    factors.push('特殊IP段 (-10分)');
  }

  if (penalty === 0) {
    factors.push('正常公网IP (+0分)');
  }
  
  return { penalty, factors };
}

/**
 * 检查是否为私有IP
 */
function isPrivateIP(ip) {
  if (ip.includes(':')) {
    // IPv6私有地址
    return ip.startsWith('fc') || ip.startsWith('fd') || ip.startsWith('fe80');
  } else {
    // IPv4私有地址
    const parts = ip.split('.').map(Number);
    return (
      (parts[0] === 10) ||
      (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
      (parts[0] === 192 && parts[1] === 168) ||
      (parts[0] === 127)
    );
  }
}

/**
 * 检查是否为保留IP
 */
function isReservedIP(ip) {
  if (ip.includes(':')) {
    // IPv6保留地址
    return ip.startsWith('::') || ip.startsWith('2001:db8');
  } else {
    // IPv4保留地址
    const parts = ip.split('.').map(Number);
    return (
      (parts[0] === 0) ||
      (parts[0] === 169 && parts[1] === 254) ||
      (parts[0] >= 224)
    );
  }
}

/**
 * 生成评分说明
 */
function generateExplanation(score, factors) {
  let level = '';
  let description = '';
  
  if (score >= 90) {
    level = '优秀';
    description = 'IP质量很好，不容易被网站拦截';
  } else if (score >= 80) {
    level = '良好';
    description = 'IP质量较好，很少被拦截';
  } else if (score >= 70) {
    level = '一般';
    description = 'IP质量中等，偶尔可能被拦截';
  } else if (score >= 60) {
    level = '较差';
    description = 'IP质量不好，容易被当作机器人';
  } else {
    level = '很差';
    description = 'IP质量很差，经常被网站拦截';
  }

  // 生成简单清楚的说明
  const simpleExplanation = `IP质量分说明：
• 分数越高，IP质量越好
• 高分IP不容易被网站拦截
• 低分IP容易被当作机器人

评分等级：
• 90分以上：优秀 - IP质量很好，不容易被网站拦截
• 80-89分：良好 - IP质量较好，很少被拦截
• 70-79分：一般 - IP质量中等，偶尔可能被拦截
• 60-69分：较差 - IP质量不好，容易被当作机器人
• 60分以下：很差 - IP质量很差，经常被网站拦截

当前评级：${score}分 - ${level}
${description}`;

  return {
    level,
    description,
    details: simpleExplanation
  };
}
