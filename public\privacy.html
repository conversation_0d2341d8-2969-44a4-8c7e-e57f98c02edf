<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - 𝓌𝑜𝒷.MyIP</title>
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" media="print" onload="this.media='all'">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </noscript>
    <style>
        :root {
            --primary-gradient: linear-gradient(45deg, #3498db, #e74c3c);
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --bg-gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            color: white;
        }

        /* 动态背景 */
        .animated-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        /* 浮动粒子 */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 黑客代码雨动画 */
        .matrix-rain {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
            pointer-events: none;
        }

        .matrix-char {
            position: absolute;
            font-family: 'Courier New', monospace;
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
            animation: matrix-drop linear infinite;
            text-shadow: 0 0 8px currentColor;
            opacity: 0.7;
        }

        /* 蓝到红渐变动画 */
        @keyframes matrix-drop {
            0% {
                transform: translateY(-100px);
                color: #3498db;
                opacity: 0;
                text-shadow: 0 0 12px #3498db;
            }
            10% {
                opacity: 0.6;
            }
            25% {
                color: #2ecc71;
                text-shadow: 0 0 12px #2ecc71;
                opacity: 0.7;
            }
            50% {
                color: #f1c40f;
                text-shadow: 0 0 12px #f1c40f;
                opacity: 0.8;
            }
            75% {
                color: #e67e22;
                text-shadow: 0 0 12px #e67e22;
                opacity: 0.7;
            }
            90% {
                color: #e74c3c;
                text-shadow: 0 0 12px #e74c3c;
                opacity: 0.6;
            }
            100% {
                transform: translateY(100vh);
                color: #e74c3c;
                opacity: 0;
                text-shadow: 0 0 15px #e74c3c;
            }
        }

        /* 闪烁效果 */
        .matrix-char.glitch {
            animation: matrix-drop linear infinite, matrix-glitch 0.3s infinite;
        }

        @keyframes matrix-glitch {
            0%, 100% {
                transform: translateX(0);
                text-shadow: 0 0 8px currentColor;
            }
            25% {
                transform: translateX(-1px);
                text-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
            }
            75% {
                transform: translateX(1px);
                text-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
            }
        }

        /* 主要内容 */
        .content-wrapper {
            position: relative;
            z-index: 2;
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 返回按钮 */
        .back-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
            color: white;
            text-decoration: none;
        }

        /* 标题区域 */
        .title-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .gradient-title {
            font-size: 3.5rem;
            font-weight: bold;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% {
                filter: drop-shadow(0 0 10px rgba(52, 152, 219, 0.5));
            }
            100% {
                filter: drop-shadow(0 0 20px rgba(231, 76, 60, 0.5));
            }
        }

        .title-underline {
            width: 100px;
            height: 4px;
            background: var(--primary-gradient);
            margin: 0 auto 1rem;
            border-radius: 2px;
            animation: underlineExpand 2s ease-in-out infinite alternate;
        }

        @keyframes underlineExpand {
            0% {
                width: 100px;
            }
            100% {
                width: 200px;
            }
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            margin: 0;
        }

        /* 内容区域 */
        .content-section {
            display: grid;
            gap: 2rem;
        }

        .policy-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            animation: cardSlideIn 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .policy-card:nth-child(1) { animation-delay: 0.1s; }
        .policy-card:nth-child(2) { animation-delay: 0.2s; }
        .policy-card:nth-child(3) { animation-delay: 0.3s; }
        .policy-card:nth-child(4) { animation-delay: 0.4s; }
        .policy-card:nth-child(5) { animation-delay: 0.5s; }
        .policy-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes cardSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .policy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            color: white;
        }

        .card-header i {
            font-size: 1.5rem;
            margin-right: 0.5rem;
            color: #3498db;
        }

        .card-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .card-content {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
        }

        .policy-list {
            list-style: none;
            padding: 0;
        }

        .policy-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .policy-list li:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(10px);
        }

        .policy-list li i {
            margin-right: 0.8rem;
            color: #27ae60;
            font-size: 1.1rem;
        }

        .note {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            border-radius: 5px;
            font-style: italic;
        }

        .contact-info a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: #2980b9;
            text-decoration: underline;
        }

        /* 底部区域 */
        .footer-section {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-section p {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .footer-animation {
            display: flex;
            justify-content: center;
        }

        .pulse-dot {
            width: 20px;
            height: 20px;
            background: #e74c3c;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.5);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
            
            .gradient-title {
                font-size: 2.5rem;
            }
            
            .policy-card {
                padding: 1.5rem;
            }
        }

        /* 暗色模式 */
        @media (prefers-color-scheme: dark) {
            body {
                background: var(--bg-gradient-dark);
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="animated-background">
        <div class="floating-particles" id="particles"></div>
        <div class="matrix-rain" id="matrixRain"></div>
    </div>

    <!-- 主要内容 -->
    <div class="content-wrapper">
        <!-- 返回按钮 -->
        <a href="#" class="back-button" onclick="goBack(); return false;">
            <i class="bi bi-arrow-left"></i>
            返回
        </a>

        <!-- 标题 -->
        <div class="title-section">
            <h1 class="gradient-title">隐私政策</h1>
            <div class="title-underline"></div>
            <p class="subtitle">我们重视并保护您的隐私</p>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-shield-check"></i>
                    <h2>数据收集声明</h2>
                </div>
                <div class="card-content">
                    <p>𝓌𝑜𝒷.MyIP 致力于保护用户隐私，我们郑重声明：</p>
                    <ul class="policy-list">
                        <li><i class="bi bi-check-circle-fill"></i> 本站不会收集、存储或记录任何用户的个人信息</li>
                        <li><i class="bi bi-check-circle-fill"></i> 不会追踪用户的浏览行为或使用习惯</li>
                        <li><i class="bi bi-check-circle-fill"></i> 不会保存用户的IP地址或地理位置信息</li>
                        <li><i class="bi bi-check-circle-fill"></i> 所有IP检测和网络测试均在用户本地进行</li>
                    </ul>
                </div>
            </div>

            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-cpu"></i>
                    <h2>技术实现</h2>
                </div>
                <div class="card-content">
                    <p>我们的隐私保护通过以下技术手段实现：</p>
                    <ul class="policy-list">
                        <li><i class="bi bi-gear-fill"></i> 前端JavaScript技术，所有检测在浏览器本地执行</li>
                        <li><i class="bi bi-gear-fill"></i> 无服务器端日志记录，不保存任何用户数据</li>
                        <li><i class="bi bi-gear-fill"></i> 开源代码，透明可审计</li>
                        <li><i class="bi bi-gear-fill"></i> 使用公共API服务，不经过我们的服务器</li>
                    </ul>
                </div>
            </div>

            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-globe"></i>
                    <h2>第三方服务</h2>
                </div>
                <div class="card-content">
                    <p>本站可能使用以下第三方服务来提供功能：</p>
                    <ul class="policy-list">
                        <li><i class="bi bi-link-45deg"></i> Cloudflare - 用于IP地址检测</li>
                        <li><i class="bi bi-link-45deg"></i> IPify - 用于IP地址检测</li>
                        <li><i class="bi bi-link-45deg"></i> 各种公共DNS服务 - 用于DNS测试</li>
                        <li><i class="bi bi-link-45deg"></i> WebRTC - 用于本地网络检测</li>
                    </ul>
                    <p class="note">这些服务有各自的隐私政策，我们不对其数据处理负责。</p>
                </div>
            </div>

            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-cookie"></i>
                    <h2>Cookie 和本地存储</h2>
                </div>
                <div class="card-content">
                    <p>我们仅使用必要的本地存储来改善用户体验：</p>
                    <ul class="policy-list">
                        <li><i class="bi bi-hdd-fill"></i> 用户偏好设置（如主题、语言等）</li>
                        <li><i class="bi bi-hdd-fill"></i> 临时缓存数据以提高性能</li>
                        <li><i class="bi bi-hdd-fill"></i> 不使用追踪性Cookie</li>
                        <li><i class="bi bi-hdd-fill"></i> 所有数据仅存储在用户设备本地</li>
                    </ul>
                </div>
            </div>

            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-telephone"></i>
                    <h2>联系我们</h2>
                </div>
                <div class="card-content">
                    <p>如果您对我们的隐私政策有任何疑问，请通过以下方式联系我们：</p>
                    <div class="contact-info">
                        <p><i class="bi bi-envelope-fill"></i> Blog：通过 <a href="https://wobshare.us.kg" target="_blank">wobshare.us.kg</a> 联系</p>
                        <p><i class="bi bi-github"></i> GitHub：<a href="https://github.com/wob25" target="_blank">wob25</a></p>
                    </div>
                </div>
            </div>

            <div class="policy-card">
                <div class="card-header">
                    <i class="bi bi-calendar-check"></i>
                    <h2>更新日期</h2>
                </div>
                <div class="card-content">
                    <p>本隐私政策最后更新时间：<span id="currentDate"></span></p>
                    <p>我们保留随时更新此政策的权利，任何更改将在此页面上公布。</p>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer-section">
            <p>感谢您信任 𝓌𝑜𝒷.MyIP</p>
            <div class="footer-animation">
                <div class="pulse-dot"></div>
            </div>
        </div>
    </div>

    <script>
        // 生成浮动粒子（优化性能）
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20; // 减少粒子数量提升性能

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                const size = Math.random() * 3 + 2;
                const left = Math.random() * 100;
                const animationDelay = Math.random() * 15;
                const animationDuration = Math.random() * 8 + 15;

                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${left}%`;
                particle.style.animationDelay = `${animationDelay}s`;
                particle.style.animationDuration = `${animationDuration}s`;

                particlesContainer.appendChild(particle);
            }
        }

        // 创建黑客代码雨效果
        function createMatrixRain() {
            const matrixContainer = document.getElementById('matrixRain');
            // 只使用数字0-9和字母a-z, A-Z
            const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

            // 清空现有内容
            matrixContainer.innerHTML = '';

            let isRunning = true;
            let activeChars = 0;
            const maxChars = 10; // 减少字符数量提升性能

            // 创建字符雨滴
            function createRainDrop() {
                if (!isRunning || activeChars >= maxChars) {
                    return;
                }

                const char = document.createElement('div');
                char.className = 'matrix-char';
                activeChars++;

                // 减少闪烁效果概率
                if (Math.random() < 0.1) {
                    char.classList.add('glitch');
                }

                // 随机字符
                char.textContent = characters[Math.floor(Math.random() * characters.length)];

                // 随机位置
                char.style.left = Math.random() * window.innerWidth + 'px';

                // 固定动画时长 (5秒，保持一致的下落速度)
                char.style.animationDuration = '5s';

                // 随机延迟
                char.style.animationDelay = Math.random() * 2 + 's';

                matrixContainer.appendChild(char);

                // 动画结束后移除元素并减少计数
                char.addEventListener('animationend', () => {
                    if (char.parentNode) {
                        char.parentNode.removeChild(char);
                    }
                    activeChars--;
                });
            }

            // 持续创建雨滴，固定间隔
            function startRain() {
                if (!isRunning) return;

                createRainDrop();

                // 增加间隔到800ms，减少性能消耗
                setTimeout(startRain, 800);
            }

            // 开始下雨
            startRain();

            // 返回停止函数
            return function stopRain() {
                isRunning = false;
            };
        }

        // 全局变量存储停止函数
        let stopMatrixRain = null;

        // 清理函数
        function clearMatrixRain() {
            if (stopMatrixRain) {
                stopMatrixRain();
                stopMatrixRain = null;
            }
            const matrixContainer = document.getElementById('matrixRain');
            if (matrixContainer) {
                matrixContainer.innerHTML = '';
            }
        }

        // 页面加载完成后创建效果
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟创建动画效果，优先显示内容
            setTimeout(function() {
                createParticles();
                stopMatrixRain = createMatrixRain();
            }, 100);

            // 设置当前日期
            const currentDate = new Date().toLocaleDateString('zh-CN');
            document.getElementById('currentDate').textContent = currentDate;
        });

        // 窗口大小改变时重新创建代码雨
        window.addEventListener('resize', function() {
            clearMatrixRain();
            setTimeout(function() {
                stopMatrixRain = createMatrixRain();
            }, 100);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', clearMatrixRain);

        // 返回功能
        function goBack() {
            // 尝试使用浏览器历史记录返回
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，返回到主页
                window.location.href = '../index.html';
            }
        }
    </script>
</body>
</html>
